import {
  confirmWithCheckbox,
  getUserInput,
  performDensityAnalysis as originalPerformDensityAnalysis,
  searchAddress,
  searchDirections,
} from "@geon-ai/tools";
import { tool, generateObject } from "ai";
import { openai } from "@ai-sdk/openai";
import { z } from "zod";
import { getApiConfig, getApiUserId } from "@/lib/api-config";

// 베이스맵 목록 정의
const baseMap = {
  eMapBasic: "바로e맵 일반지도",
  eMapAIR: "바로e맵 항공지도",
  eMapColor: "바로e맵 색각지도",
  eMapWhite: "바로e맵 백지도",
} as const;

// 밀도분석 도구 - LLM용 결과 prune 기능 추가
export const performDensityAnalysis = {
  ...originalPerformDensityAnalysis,
  experimental_toToolResultContent: (result: any) => {
    // LLM에게는 텍스트 형태의 요약된 정보만 전달 (대용량 GeoJSON 데이터 제외)
    const featureCount = result.features?.length || 0;
    const analysisType =
      result.type === "FeatureCollection" ? "공간 밀도 분석" : "밀도 분석";

    const summary = `${analysisType}이 완료되었습니다. 총 ${featureCount}개의 공간 데이터를 분석하여 밀도 분포를 계산했습니다. 분석 결과가 지도에 히트맵 형태로 시각화되었습니다.`;

    return [
      {
        type: "text" as const,
        text: summary,
      },
    ];
  },
};

// 주소검색 도구 - LLM용 결과 prune 기능 추가
export const searchAddressOptimized = {
  ...searchAddress,
  experimental_toToolResultContent: (result: any) => {
    // LLM에게는 핵심 정보만 전달 (상세한 메타데이터 제외)
    if (!result.result?.jusoList?.length) {
      return [
        {
          type: "text" as const,
          text: "검색 결과가 없습니다. 다른 키워드로 다시 검색해보세요.",
        },
      ];
    }

    const addresses = result.result.jusoList;
    const addressCount = addresses.length;

    // 최대 3개까지만 LLM에 전달
    const topAddresses = addresses.slice(0, 3).map((addr: any) => ({
      roadAddr: addr.roadAddr,
      buildName: addr.buildName || addr.poiName,
      buildLo: addr.buildLo, // 경도 (X좌표)
      buildLa: addr.buildLa, // 위도 (Y좌표)
    }));

    const summary = `주소 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;

    let addressInfo = topAddresses
      .map(
        (addr: any, index: number) =>
          `${index + 1}. ${addr.roadAddr}${
            addr.buildName ? ` (${addr.buildName})` : ""
          } - 좌표: ${addr.buildLo},${addr.buildLa}`
      )
      .join("\n");

    if (addressCount > 3) {
      addressInfo += `\n... 외 ${addressCount - 3}개 추가 결과`;
    }

    return [
      {
        type: "text" as const,
        text: `${summary}\n\n${addressInfo}`,
      },
    ];
  },
};

// 출발지 검색 도구 - searchAddress와 동일한 로직이지만 UI에서 구분 가능
export const searchOrigin = {
  ...searchAddress,
  description:
    "출발지 위치를 검색합니다. 경로 탐색의 시작점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",
  experimental_toToolResultContent: (result: any) => {
    if (!result.result?.jusoList?.length) {
      return [
        {
          type: "text" as const,
          text: "출발지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요.",
        },
      ];
    }

    const addresses = result.result.jusoList;
    const addressCount = addresses.length;
    const topAddresses = addresses.slice(0, 3).map((addr: any) => ({
      roadAddr: addr.roadAddr,
      buildName: addr.buildName || addr.poiName,
      buildLo: addr.buildLo,
      buildLa: addr.buildLa,
    }));

    const summary = `출발지 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;
    let addressInfo = topAddresses
      .map(
        (addr: any, index: number) =>
          `${index + 1}. ${addr.roadAddr}${
            addr.buildName ? ` (${addr.buildName})` : ""
          } - 좌표: ${addr.buildLo},${addr.buildLa}`
      )
      .join("\n");

    if (addressCount > 3) {
      addressInfo += `\n... 외 ${addressCount - 3}개 추가 결과`;
    }

    return [
      {
        type: "text" as const,
        text: `${summary}\n\n${addressInfo}`,
      },
    ];
  },
};

// 목적지 검색 도구 - searchAddress와 동일한 로직이지만 UI에서 구분 가능
export const searchDestination = {
  ...searchAddress,
  description:
    "목적지 위치를 검색합니다. 경로 탐색의 도착점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",
  experimental_toToolResultContent: (result: any) => {
    if (!result.result?.jusoList?.length) {
      return [
        {
          type: "text" as const,
          text: "목적지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요.",
        },
      ];
    }

    const addresses = result.result.jusoList;
    const addressCount = addresses.length;
    const topAddresses = addresses.slice(0, 3).map((addr: any) => ({
      roadAddr: addr.roadAddr,
      buildName: addr.buildName || addr.poiName,
      buildLo: addr.buildLo,
      buildLa: addr.buildLa,
    }));

    const summary = `목적지 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;
    let addressInfo = topAddresses
      .map(
        (addr: any, index: number) =>
          `${index + 1}. ${addr.roadAddr}${
            addr.buildName ? ` (${addr.buildName})` : ""
          } - 좌표: ${addr.buildLo},${addr.buildLa}`
      )
      .join("\n");

    if (addressCount > 3) {
      addressInfo += `\n... 외 ${addressCount - 3}개 추가 결과`;
    }

    return [
      {
        type: "text" as const,
        text: `${summary}\n\n${addressInfo}`,
      },
    ];
  },
};

// 길찾기 도구 - LLM용 결과 prune 기능 추가 + avoid 파라미터 제거
export const searchDirectionsOptimized = {
  ...searchDirections,
  execute: async (params: any, options: any) => {
    // avoid 파라미터 제거 (API 오류 방지)
    const { avoid, ...cleanParams } = params;
    console.log("searchDirections - avoid 파라미터 제거됨:", avoid);
    return searchDirections.execute(cleanParams, options);
  },
  experimental_toToolResultContent: (result: any) => {
    // LLM에게는 핵심 정보만 전달 (상세한 경로 데이터 제외)

    // 에러 처리
    if (result.error) {
      return [
        {
          type: "text" as const,
          text: `경로 탐색 실패: ${result.error}`,
        },
      ];
    }

    // routes 배열에서 첫 번째 경로의 정보 추출
    if (!result.routes || !result.routes.length) {
      return [
        {
          type: "text" as const,
          text: "경로를 찾을 수 없습니다. 출발지와 목적지를 확인해주세요.",
        },
      ];
    }

    const route = result.routes[0];

    // result_code로 성공/실패 판단
    if (route.result_code !== 0) {
      return [
        {
          type: "text" as const,
          text: `경로 탐색 실패: ${
            route.result_msg || "알 수 없는 오류가 발생했습니다."
          }`,
        },
      ];
    }

    // 성공한 경우 핵심 정보만 추출
    const summary = route.summary;
    const distance = summary?.distance
      ? `${(summary.distance / 1000).toFixed(1)}km`
      : "정보 없음";
    const duration = summary?.duration
      ? `${Math.floor(summary.duration / 60)}분`
      : "정보 없음";
    const taxiFare = summary?.fare?.taxi
      ? `${summary.fare.taxi.toLocaleString()}원`
      : "정보 없음";
    const tollFare = summary?.fare?.toll
      ? `${summary.fare.toll.toLocaleString()}원`
      : "무료";

    // 출발지/목적지 정보
    const originName = result.origin?.name || "출발지";
    const destinationName = result.destination?.name || "목적지";

    const routeInfo = `경로 탐색이 완료되었습니다.

📍 ${originName} → ${destinationName}
🚗 거리: ${distance}
⏱️ 소요시간: ${duration}
💰 예상 택시요금: ${taxiFare}
🛣️ 통행료: ${tollFare}

경로가 지도에 자동으로 표시되었습니다.`;

    return [
      {
        type: "text" as const,
        text: routeInfo,
      },
    ];
  },
};

// 커스텀 베이스맵 도구들
export const getBasemapList = tool({
  description: `사용 가능한 배경지도 목록을 조회합니다.
  반환되는 배경지도 목록:
  - eMapBasic: 바로e맵 일반지도 (도로, 건물, 지형지물 포함)
  - eMapAIR: 바로e맵 항공지도 (위성 항공사진)
  - eMapColor: 바로e맵 색각지도 (색상 대비 강화)
  - eMapWhite: 바로e맵 백지도 (깔끔한 흰색 배경)`,
  parameters: z.object({}),
  execute: async () => {
    return {
      basemaps: Object.entries(baseMap).map(([id, name]) => ({
        id,
        name,
        displayName: name,
      })),
    };
  },
});

export const changeBasemap = tool({
  description: `배경지도를 변경합니다.
  사용 가능한 배경지도 ID:
  - eMapBasic: 일반지도 (기본 지도)
  - eMapAIR: 항공지도 (위성지도)
  - eMapColor: 색각지도 (색상 대비)
  - eMapWhite: 백지도 (심플한 배경)

  키워드 매칭:
  - "위성지도", "항공지도" → eMapAIR
  - "일반지도", "기본지도" → eMapBasic
  - "색각지도", "컬러지도" → eMapColor
  - "백지도", "흰지도" → eMapWhite`,
  parameters: z.object({
    basemapId: z
      .enum(["eMapBasic", "eMapAIR", "eMapColor", "eMapWhite"])
      .describe(
        "변경할 배경지도의 ID (eMapBasic, eMapAIR, eMapColor, eMapWhite 중 하나)"
      ),
  }),
  execute: async ({ basemapId }) => {
    const basemapName = baseMap[basemapId];

    if (!basemapName) {
      return {
        error: `지원하지 않는 배경지도 ID입니다: ${basemapId}`,
        availableBasemaps: Object.keys(baseMap),
      };
    }

    return {
      basemap: basemapId,
      basemapId: basemapId,
      basemapName: basemapName,
      success: true,
      message: `배경지도가 ${basemapName}로 변경되었습니다.`,
    };
  },
});

export const setMapCenter = tool({
  description: `지도의 중심점을 특정 좌표로 이동합니다.

  사용 예시:
  - 특정 좌표로 이동: "지도를 127.027926, 37.497175로 이동"
  - 상대적 이동: "지도를 동쪽으로 500m 이동", "북쪽으로 1km 이동"
  - 방향별 이동: "위로", "아래로", "왼쪽으로", "오른쪽으로"`,
  parameters: z.object({
    longitude: z.number().describe("경도 (X좌표)"),
    latitude: z.number().describe("위도 (Y좌표)"),
    moveType: z
      .enum(["absolute", "relative"])
      .describe("이동 타입: absolute(절대좌표), relative(상대이동)")
      .optional()
      .default("absolute"),
    description: z.string().describe("이동에 대한 설명").optional(),
  }),
  execute: async ({ longitude, latitude, moveType, description }) => {
    try {
      return {
        success: true,
        center: [longitude, latitude],
        moveType,
        message:
          description ||
          `지도 중심점이 경도 ${longitude}, 위도 ${latitude}로 이동되었습니다.`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: `지도 중심점 이동 실패: ${error.message}`,
      };
    }
  },
});

export const setMapZoom = tool({
  description: `지도의 확대/축소 레벨을 조정합니다.

  확대/축소 레벨:
  - 1-5: 국가/대륙 레벨 (매우 넓은 범위)
  - 6-10: 지역/도시 레벨 (넓은 범위)
  - 11-15: 구/동 레벨 (중간 범위)
  - 16-20: 건물/도로 레벨 (상세 범위)

  사용 예시:
  - "지도 확대해줘" → zoomDirection: "in", zoomLevel: 2, zoomType: "relative"
  - "더 넓게 보여줘" → zoomDirection: "out", zoomLevel: 2, zoomType: "relative"
  - "최대한 확대" → zoomLevel: 18, zoomType: "absolute"
  - "전체 보기" → zoomLevel: 2, zoomType: "absolute"
`,
  parameters: z.object({
    zoomLevel: z
      .number()
      .optional()
      .describe("확대/축소 레벨 또는 변경량 (1-20)"),
    zoomType: z
      .enum(["absolute", "relative"])
      .describe("확대/축소 타입: absolute(절대레벨), relative(상대변경)")
      .optional()
      .default("relative"),
    zoomDirection: z
      .enum(["in", "out"])
      .describe("확대/축소 방향: in(확대), out(축소)"),
    description: z.string().describe("확대/축소에 대한 설명").optional(),
  }),
  execute: async ({ zoomLevel = 2, zoomType, zoomDirection, description }) => {
    try {
      return {
        success: true,
        zoom: zoomLevel,
        zoomType,
        zoomDirection,
        message:
          description ||
          `지도 ${zoomDirection === "in" ? "확대" : "축소"} 레벨이 ${
            zoomType === "relative" ? "변경" : "설정"
          }되었습니다.`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: `지도 확대/축소 실패: ${error.message}`,
      };
    }
  },
});

export const moveMapByDirection = tool({
  description: `지도를 특정 방향으로 이동합니다.

  지원하는 방향:
  - north, up, 위, 북쪽: 북쪽으로 이동
  - south, down, 아래, 남쪽: 남쪽으로 이동
  - east, right, 오른쪽, 동쪽: 동쪽으로 이동
  - west, left, 왼쪽, 서쪽: 서쪽으로 이동

  거리 형식:
  - "500m", "1km", "2000m" 등의 형태로 입력
  - 단위가 없으면 미터(m)로 간주
  - 기본값: "500m"

  좌표계 지원:
  - EPSG:5186 (Korea 2000 / Central Belt 2010): 미터 단위 직접 계산
  - 정확한 거리 이동을 위해 투영 좌표계 사용`,
  parameters: z.object({
    direction: z
      .enum(["north", "south", "east", "west", "up", "down", "left", "right"])
      .describe("이동 방향"),
    distance: z
      .string()
      .describe("이동 거리 (예: '500m', '1km', '2000m')")
      .optional()
      .default("500m"),
  }),
  execute: async ({ direction, distance }) => {
    try {
      // 거리 문자열 파싱 (예: "500m", "1km", "2000")
      const parseDistance = (distanceStr: string): number => {
        const match = distanceStr.match(/^(\d+(?:\.\d+)?)\s*(m|km)?$/i);
        if (!match) {
          throw new Error(`잘못된 거리 형식: ${distanceStr}`);
        }

        const value = parseFloat(match[1]);
        const unit = match[2]?.toLowerCase() || 'm';

        return unit === 'km' ? value * 1000 : value;
      };

      const distanceInMeters = parseDistance(distance);
      console.log(`moveMapByDirection: ${direction}, ${distance} -> ${distanceInMeters}m`);

      // EPSG:5186 좌표계에서는 미터 단위로 직접 계산
      // 이 좌표계는 한국 중부 지역에 최적화된 투영 좌표계로 미터 단위 사용
      let deltaX = 0; // 동서 방향 (X축)
      let deltaY = 0; // 남북 방향 (Y축)

      switch (direction) {
        case "north":
        case "up":
          deltaY = distanceInMeters; // 북쪽으로 이동 (Y 증가)
          break;
        case "south":
        case "down":
          deltaY = -distanceInMeters; // 남쪽으로 이동 (Y 감소)
          break;
        case "east":
        case "right":
          deltaX = distanceInMeters; // 동쪽으로 이동 (X 증가)
          break;
        case "west":
        case "left":
          deltaX = -distanceInMeters; // 서쪽으로 이동 (X 감소)
          break;
      }

      const directionNames = {
        north: "북쪽",
        south: "남쪽",
        east: "동쪽",
        west: "서쪽",
        up: "위쪽",
        down: "아래쪽",
        left: "왼쪽",
        right: "오른쪽",
      };

      return {
        success: true,
        direction,
        deltaX, // EPSG:5186에서는 X, Y 좌표 사용
        deltaY,
        distance: distanceInMeters,
        coordinateSystem: "EPSG:5186",
        message: `지도가 ${directionNames[direction]}으로 ${distance} 이동되었습니다.`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: `지도 방향 이동 실패: ${error.message}`,
      };
    }
  },
});



export const chooseOption = tool({
  description:
    "사용자에게 여러 선택지 중 하나를 고르도록 요청합니다. 결과는 options 배열의 값 중 하나입니다.",
  parameters: z.object({
    message: z.string().describe("사용자에게 보여줄 안내 문구"),
    options: z
      .array(
        z
          .string()
          .describe(
            "사용자에게 표시될 텍스트. 반드시 'key|value' 형태로 제공하세요."
          )
      )
      .describe("사용자가 선택할 수 있는 옵션 문자열 배열"),
  }),
});

export const getLayerList = tool({
  description: `레이어 목록을 검색합니다.
**레이어 타입 매핑 (lyrTySeCode): 명시하지 않는 경우 반드시 값을 비워둡니다**
`,
  parameters: z.object({
    userId: z.string().describe("사용자 ID"),
    layerName: z.string().optional().describe("레이어 이름"),
    lyrTySeCode: z
      .string()
      .optional()
      .describe("레이어 유형 코드 ('1': 점, '2': 선, '3': 면, 비어있는 경우 전체)"),
    holdDataSeCode: z
      .string()
      .optional()
      .describe(
        "데이터 구분 코드 ('0': 전체, '1': 사용자, '2': 공유, '9': 국가 (기본값 '0'))"
      ),
    pageIndex: z
      .string()
      .optional()
      .describe("조회할 페이지 인덱스 (기본값 '1')"),
    pageSize: z
      .string()
      .optional()
      .describe("한 페이지당 조회할 레이어 수 (기본값 '10')"),
  }),
  async execute({
    userId,
    layerName = "",
    lyrTySeCode = "",
    holdDataSeCode = "0",
    pageIndex = "1",
    pageSize = "10",
  }) {
    try {
      const config = getApiConfig();

      const params = new URLSearchParams({
        userId: getApiUserId(config), // 항상 geonuser 계정 사용
        holdDataSeCode,
        pageIndex,
        pageSize,
      });

      // layerName이 있을 경우에만 searchTxt 파라미터 추가
      if (layerName && layerName.trim() !== "") {
        params.append("searchTxt", layerName.trim());
      }

      if (lyrTySeCode && lyrTySeCode.trim() !== "") {
        params.append("lyrTySeCode", lyrTySeCode.trim());
      }

      // 인증 정보 추가
      if (config.headers.crtfckey) {
        params.append("crtfckey", config.headers.crtfckey);
      }

      const response = await fetch(
        `${config.baseUrl}/smt/layer/info/list?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            ...config.headers,
          },
        }
      );

      if (!response.ok) {
        response.json().then((data) => {
          console.error("API request failed with data", data);
        });
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = (await response.json()) as any;

      if (!data || !data.result) {
        return { error: "레이어 목록 조회 실패: 응답 데이터가 없습니다." };
      }

      return data;
    } catch (error: any) {
      return { error: `레이어 목록 조회 실패: ${error.message}` };
    }
  },
});

// 레이어 스타일 업데이트 도구
export const updateLayerStyle = tool({
  description: `레이어의 시각적 스타일을 변경합니다. 레이어 타입(점/선/면)에 따라 적절한 스타일이 적용됩니다.

**지원하는 스타일 속성:**
- color: 기본 색상 (hex 코드, 예: "#FF0000") - 모든 타입
- fillOpacity: 채우기 투명도 (0.0-1.0) - 점, 면 타입
- strokeColor: 윤곽선 색상 (hex 코드) - 모든 타입
- strokeWidth: 윤곽선 두께 (픽셀) - 모든 타입
- radius: 점 크기 (픽셀) - 점 타입만
- width: 선 두께 (픽셀) - 선 타입만
- symbol: 심볼 타입 (점 타입만) - "circle", "square", "triangle", "star", "cross", "x"

**레이어 타입별 적용:**
- 점(Point) 레이어: color, fillOpacity, strokeColor, strokeWidth, radius, symbol
- 선(Line) 레이어: color, strokeColor, strokeWidth, width
- 면(Polygon) 레이어: color, fillOpacity, strokeColor(윤곽선), strokeWidth(윤곽선)

**사용 예시:**
- "빨간색으로 바꿔줘" → color: "#FF0000"
- "투명하게 해줘" → fillOpacity: 0.3
- "윤곽선을 두껍게" → strokeWidth: 3
- "크기를 키워줘" → radius: 10 (점) 또는 width: 5 (선)
- "별 모양으로 바꿔줘" → symbol: "star"
- "사각형으로 바꿔줘" → symbol: "square"
- "십자 모양으로 해줘" → symbol: "cross"`,
  parameters: z.object({
    layerId: z.string().describe("스타일을 변경할 레이어 ID"),
    color: z.string().optional().describe("기본 색상 (hex 코드, 예: #FF0000)"),
    fillOpacity: z.number().min(0).max(1).optional().describe("채우기 투명도 (0.0-1.0)"),
    strokeColor: z.string().optional().describe("윤곽선 색상 (hex 코드)"),
    strokeWidth: z.number().min(0).max(20).optional().describe("윤곽선 두께 (픽셀)"),
    radius: z.number().min(1).max(50).optional().describe("점 크기 (픽셀, 점 타입만)"),
    width: z.number().min(1).max(20).optional().describe("선 두께 (픽셀, 선 타입만)"),
    symbol: z.enum(["circle", "square", "triangle", "star", "cross", "x"]).optional().describe("심볼 타입 (점 타입만): circle(원), square(사각형), triangle(삼각형), star(별), cross(십자), x(X자)"),
    description: z.string().optional().describe("변경 사항에 대한 설명")
  }),
  async execute({ layerId, color, fillOpacity, strokeColor, strokeWidth, radius, width, symbol, description }) {
    try {
      // 스타일 객체 구성
      const styleUpdate: any = {};

      if (color) styleUpdate.color = color;
      if (fillOpacity !== undefined) styleUpdate.fillOpacity = fillOpacity;
      if (strokeColor) styleUpdate.strokeColor = strokeColor;
      if (strokeWidth !== undefined) styleUpdate.strokeWidth = strokeWidth;
      if (radius !== undefined) styleUpdate.radius = radius;
      if (width !== undefined) styleUpdate.width = width;
      if (symbol) styleUpdate.symbol = symbol;

      return {
        layerId,
        styleUpdate,
        description: description || "레이어 스타일이 업데이트되었습니다.",
        success: true
      };
    } catch (error: any) {
      return {
        error: `스타일 업데이트 실패: ${error.message}`,
        success: false
      };
    }
  },
});

// 유형별 스타일 생성 도구 (단순화된 파라미터)
export const generateCategoricalStyle = tool({
  description: `레이어에 여러 조건별 스타일을 적용합니다. 사용자가 "A는 빨간색, B는 파란색" 같은 요청을 할 때 사용합니다.
attributes 매개변수로 filter 조건에 사용할 수 있는 속성 정보를 제공해야 합니다.
예시: 
`,
  parameters: z.object({
    layerId: z.string().describe("스타일을 적용할 레이어 ID"),
    lyrNm: z.string().describe("레이어이름"),
    attributes: z.string().describe("🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요"),
    userInstruction: z.string().describe("사용자의 자연어 스타일링 요청 (예: '용산구는 노란색, 강남구는 파란색, 나머지는 회색으로', '벽돌구조이면서 1990년도 이전인 건물은 빨간색으로')")
  }),
  async execute({ layerId, lyrNm, attributes, userInstruction }) {
    try {
      console.log("generateCategoricalStyle called with:", {
        layerId,
        lyrNm,
        attributes,
        userInstruction
      });

      // generateObject를 사용해서 userInstruction에서 바로 processedStyleRules 생성
      const { object: processedStyleRules } = await generateObject({
        model: openai("gpt-4.1-nano", { structuredOutputs: true }),
        schema: z.object({
          styleRules: z.array(z.object({
            description: z.string().describe("규칙 설명"),
            color: z.string().describe("HEX 색상 코드 (예: #FF0000, #0000FF, #00FF00)"),
            conditions: z.array(z.object({
              attributeName: z.string().describe("속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용"),
              condition: z.enum(["like", "equal", "greater", "less", "greaterEqual", "lessEqual", "default"]).describe("조건 타입"),
              value: z.string().describe("조건 값")
            })).describe("조건 배열 (복수 조건 지원). default 조건인 경우 빈 배열"),
            logicalOperator: z.enum(["AND", "OR"]).describe("복수 조건 간 논리 연산자 (기본값: AND)")
          }))
        }),
        prompt: `다음 자연어 스타일링 요청을 분석해서 스타일 규칙으로 변환해주세요:

**사용자 요청**: "${userInstruction}"
**레이어명**: "${lyrNm}"
**사용 가능한 속성들**: "${attributes}"

**🚨 중요 제약사항 🚨**:
- **반드시 위의 "사용 가능한 속성들"에서 속성명을 추출하여 사용하세요**
- 속성 형식: "a17(건폐율), a13(사용승인일자)" → 속성명은 "a17", "a13" 사용
- 속성 형식: "c1(주소), c2(건물명)" → 속성명은 "c1", "c2" 사용
- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**
- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요

**변환 규칙**:
1. 자연어에서 조건과 색상을 추출하여 스타일 규칙 생성
2. 색상은 HEX 코드로 직접 생성 (예: #FF0000, #0000FF, #00FF00, #FFFF00, #800080, #FFA500, #FFC0CB, #A52A2A, #808080, #000000, #FFFFFF)
3. **속성명 선택 규칙**:
   - 사용자 요청을 분석하여 위의 "사용 가능한 속성들" 목록에서만 적절한 속성을 선택
   - 목록에 없는 속성은 절대 사용하지 마세요
   - **복합 조건**: 여러 속성을 조합하여 사용 가능 (단, 모두 위 목록에 있는 속성만)
4. **🚨 복합 조건 처리 규칙 🚨**:
   - **"그리고", "이면서", "동시에", "또한"**: 하나의 규칙으로 처리, conditions 배열에 여러 조건 포함
   - **"또는", "이거나"**: 하나의 규칙으로 처리, logicalOperator를 "OR"로 설정
   - **"A는 X색, B는 Y색"**: 별개의 규칙들로 분리
   - **단일 조건**: conditions 배열에 하나의 조건만 포함
5. 조건 타입:
   - "like": 텍스트 포함 검색 (기본값, 대부분의 경우)
   - "equal": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)
   - "greater": 숫자 > 비교 (명시적으로 "초과", "보다 큰" 등이 언급된 경우)
   - "less": 숫자 < 비교 (명시적으로 "미만", "보다 작은" 등이 언급된 경우)
   - "greaterEqual": 숫자 >= 비교 (명시적으로 "이상" 등이 언급된 경우)
   - "lessEqual": 숫자 <= 비교 (명시적으로 "이하" 등이 언급된 경우)
   - "default": 기본/나머지 스타일 (조건 없음)

**변환 예시** (속성명 추출 방법):

**🚨 중요: "그리고", "이면서", "동시에" 등은 하나의 규칙으로 처리! 🚨**

**속성명 추출 방법:**
- 속성 정보: "a17(건폐율), a13(사용승인일자)" → 사용할 속성명: "a17", "a13"
- 속성 정보: "c1(주소), c2(건물명)" → 사용할 속성명: "c1", "c2"
- 속성 정보: "정제지번주소" → 사용할 속성명: "정제지번주소"

**단일 조건:**
- "서울에 있는 데이터만 파란색으로" (속성: "정제지번주소") →
  [{"description": "서울 지역 - 파란색", "color": "#0000FF", "conditions": [{"attributeName": "정제지번주소", "condition": "like", "value": "서울"}], "logicalOperator": "AND"}]

**복합 조건 (하나의 규칙):**
- "건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물" (속성: "a17(건폐율), a13(사용승인일자)") →
  [{"description": "건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물 - 빨간색", "color": "#FF0000", "conditions": [{"attributeName": "a17", "condition": "greaterEqual", "value": "50"}, {"attributeName": "a13", "condition": "less", "value": "1980"}], "logicalOperator": "AND"}]

**여러 규칙 (별개 조건):**
- "대학은 파란색, 중학교는 빨간색으로" (속성: 교육기관타입) →
  [
    {"description": "대학 - 파란색", "color": "#0000FF", "conditions": [{"attributeName": "교육기관타입", "condition": "like", "value": "대학"}], "logicalOperator": "AND"},
    {"description": "중학교 - 빨간색", "color": "#FF0000", "conditions": [{"attributeName": "교육기관타입", "condition": "like", "value": "중학교"}], "logicalOperator": "AND"}
  ]

- "나머지/기본 스타일" →
  [{"description": "나머지 - 회색", "color": "#808080", "conditions": [], "logicalOperator": "AND"}]

**중요**:
- 특별한 언급이 없으면 condition은 "like" 사용
- **logicalOperator는 항상 포함**: 단일 조건이어도 "AND" 명시, 복합 조건은 "AND" 또는 "OR" 명시
- **항상 default 규칙을 포함**: 사용자가 특정 조건만 언급해도 나머지 데이터를 위한 기본 스타일(회색 #808080)을 자동으로 추가
- 사용자가 "나머지는 X색으로" 명시한 경우에만 해당 색상 사용, 그렇지 않으면 기본 회색 사용
- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`
      });

      console.log("Processed style rules:", processedStyleRules.styleRules);

      // logicalOperator가 누락된 경우 기본값 설정
      const normalizedStyleRules = processedStyleRules.styleRules.map(rule => ({
        ...rule,
        logicalOperator: rule.logicalOperator || "AND"
      }));

      // default 규칙이 없으면 자동으로 추가
      const hasDefaultRule = normalizedStyleRules.some(rule =>
        rule.conditions.length === 0 || rule.conditions.some(cond => cond.condition === 'default')
      );
      let finalStyleRules = [...normalizedStyleRules];

      if (!hasDefaultRule) {
        finalStyleRules.push({
          description: "나머지 - 기본 스타일",
          color: "#808080", // 기본 회색
          conditions: [], // 빈 배열은 default 규칙을 의미
          logicalOperator: "AND" as const
        });
        console.log("Added default rule automatically");
      }

      return {
        layerId,
        attributes,
        styleRules: finalStyleRules,
        description: `${finalStyleRules.length}개 유형별 스타일 적용 (${attributes.length}개 속성 활용)`,
        success: true,
        type: 'categorical'
      };
    } catch (error: any) {
      return {
        error: `유형별 스타일 생성 실패: ${error.message}`,
        success: false
      };
    }
  },
});

// 레이어 삭제 도구
export const removeLayer = tool({
  description: `지도에서 레이어를 삭제합니다. 삭제된 레이어는 복구할 수 없으므로 신중하게 사용하세요.

**사용 방법:**
- layerId: 삭제할 레이어의 ID를 정확히 입력하세요
- Current map state에서 레이어 ID를 확인할 수 있습니다

**사용 예시:**
- "스타벅스 레이어를 삭제해줘" → layerId: "LR0000001234"
- "이 레이어를 지워줘" → 현재 활성 레이어의 ID 사용
- "모든 레이어를 삭제해줘" → 각 레이어 ID를 순차적으로 삭제`,
  parameters: z.object({
    layerId: z.string().describe("삭제할 레이어의 ID"),
    description: z.string().optional().describe("삭제 사유 또는 설명")
  }),
  async execute({ layerId, description }) {
    try {
      console.log("removeLayer called with layerId:", layerId);
      console.log("description:", description);
      return {
        layerId,
        description: description || "레이어가 삭제되었습니다.",
        success: true
      };
    } catch (error: any) {
      return {
        error: `레이어 삭제 실패: ${error.message}`,
        success: false
      };
    }
  },
});

export const createLayerFilter = tool({
  description: `레이어의 속성정보를 기반으로 CQL 필터를 생성합니다. 단일 조건과 복수 조건을 모두 지원합니다.

  필드 선택 가이드:
  - getLayerAttributes 도구로 조회한 properties의 필드명을 사용하세요
  - 필드명은 정확히 일치해야 합니다 (대소문자 구분)

  연산자 사용 가이드:
  - 텍스트: LIKE (부분일치), = (완전일치)
  - 숫자: >, >=, <, <=, =
  - 목록: IN

  복수 조건 지원:
  - AND: 모든 조건이 참이어야 함
  - OR: 하나 이상의 조건이 참이면 됨
  - 예시: "서울이면서 인구 100만 이상", "강남구 또는 서초구"

  Args:
    lyr_id: 필터 적용할 레이어 ID
    attributes: 사용 가능한 속성 정보 (형식: "a17(건폐율), a13(사용승인일자)")
    user_instruction: 자연어 필터링 요청 (예: "서울이면서 인구 100만 이상", "강남구 또는 서초구")
     `,
  parameters: z.object({
    lyrId: z.string().min(1).describe("필터를 적용할 레이어 ID"),
    attributes: z.string().describe("🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요"),
    userInstruction: z.string().describe("사용자의 자연어 필터링 요청 (예: '서울이면서 인구 100만 이상', '강남구 또는 서초구', '건폐율 50 이상')")
  }),
  execute: async ({
    lyrId,
    attributes,
    userInstruction,
  }) => {
    try {
      console.log("createLayerFilter called with:", {
        lyrId,
        attributes,
        userInstruction
      });

      // generateObject를 사용해서 userInstruction에서 바로 필터 조건 생성
      const { object: filterConditions } = await generateObject({
        model: openai("gpt-4.1-nano", { structuredOutputs: true }),
        schema: z.object({
          conditions: z.array(z.object({
            attributeName: z.string().describe("속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용"),
            operator: z.enum(["=", ">", "<", ">=", "<=", "LIKE", "IN"]).describe("비교 연산자"),
            value: z.string().describe("조건 값")
          })).describe("필터 조건 배열"),
          logicalOperator: z.enum(["AND", "OR"]).describe("복수 조건 간 논리 연산자 (기본값: AND)")
        }),
        prompt: `다음 자연어 필터링 요청을 분석해서 CQL 필터 조건으로 변환해주세요:

**사용자 요청**: "${userInstruction}"
**사용 가능한 속성들**: "${attributes}"

**🚨 중요 제약사항 🚨**:
- **반드시 위의 "사용 가능한 속성들"에서 속성명을 추출하여 사용하세요**
- 속성 형식: "a17(건폐율), a13(사용승인일자)" → 속성명은 "a17", "a13" 사용
- 속성 형식: "c1(주소), c2(건물명)" → 속성명은 "c1", "c2" 사용
- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**
- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요

**변환 규칙**:
1. 자연어에서 조건을 추출하여 필터 조건 생성
2. **속성명 선택 규칙**:
   - 사용자 요청을 분석하여 위의 "사용 가능한 속성들" 목록에서만 적절한 속성을 선택
   - 목록에 없는 속성은 절대 사용하지 마세요
3. **논리 연산자 처리 규칙**:
   - **"그리고", "이면서", "동시에", "또한"**: logicalOperator를 "AND"로 설정
   - **"또는", "이거나"**: logicalOperator를 "OR"로 설정
   - **단일 조건**: logicalOperator를 "AND"로 설정
4. 연산자 타입:
   - "LIKE": 텍스트 포함 검색 (기본값, 대부분의 경우)
   - "=": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)
   - ">": 숫자 > 비교 (명시적으로 "초과", "보다 큰" 등이 언급된 경우)
   - "<": 숫자 < 비교 (명시적으로 "미만", "보다 작은" 등이 언급된 경우)
   - ">=": 숫자 >= 비교 (명시적으로 "이상" 등이 언급된 경우)
   - "<=": 숫자 <= 비교 (명시적으로 "이하" 등이 언급된 경우)
   - "IN": 목록 값 매칭 (여러 값 중 하나와 일치)

**변환 예시**:
- "서울에 있는 데이터만" (속성: "정제지번주소") →
  {"conditions": [{"attributeName": "정제지번주소", "operator": "LIKE", "value": "서울"}], "logicalOperator": "AND"}

- "건폐율 50 이상이고 사용승인일자가 1980년 이전" (속성: "a17(건폐율), a13(사용승인일자)") →
  {"conditions": [{"attributeName": "a17", "operator": ">=", "value": "50"}, {"attributeName": "a13", "operator": "<", "value": "1980"}], "logicalOperator": "AND"}

- "강남구 또는 서초구" (속성: "구명") →
  {"conditions": [{"attributeName": "구명", "operator": "LIKE", "value": "강남구"}, {"attributeName": "구명", "operator": "LIKE", "value": "서초구"}], "logicalOperator": "OR"}

**중요**:
- 특별한 언급이 없으면 operator는 "LIKE" 사용
- **logicalOperator는 항상 포함**: 단일 조건이어도 "AND" 명시
- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`
      });

      console.log("Processed filter conditions:", filterConditions);

      // CQL 필터 문자열 생성
      let filterStr = "";
      if (filterConditions.conditions.length === 0) {
        return { error: "필터 조건이 생성되지 않았습니다." };
      }

      if (filterConditions.conditions.length === 1) {
        // 단일 조건
        const condition = filterConditions.conditions[0];
        filterStr = buildCQLCondition(condition);
      } else {
        // 복수 조건
        const conditionStrings = filterConditions.conditions.map(condition =>
          buildCQLCondition(condition)
        );
        const operator = filterConditions.logicalOperator || "AND";
        filterStr = conditionStrings.join(` ${operator} `);
      }

      return {
        lyr_id: lyrId,
        filter: filterStr,
        description: `${filterConditions.conditions.length}개 조건으로 필터링: ${userInstruction}`,
        conditions: filterConditions.conditions,
        logicalOperator: filterConditions.logicalOperator
      };
    } catch (error: any) {
      return { error: `필터 생성 실패: ${error.message}` };
    }
  },
});

// CQL 조건 문자열 생성 헬퍼 함수
function buildCQLCondition(condition: { attributeName: string; operator: string; value: string }): string {
  const { attributeName, operator, value } = condition;

  // 공백과 따옴표 제거
  const cleanFieldName = attributeName.trim().replace(/['"]/g, "");

  if (operator.toUpperCase() === "LIKE") {
    return `"${cleanFieldName}" LIKE '%${value}%'`;
  } else if (operator.toUpperCase() === "IN") {
    const values = value.split(",").map((v) => v.trim());
    const valueStr = values.map((v) => `'${v}'`).join(",");
    return `"${cleanFieldName}" IN (${valueStr})`;
  } else {
    const isNumeric = !isNaN(Number(value));
    return `"${cleanFieldName}"${operator}${isNumeric ? value : `'${value}'`}`;
  }
}

// 레이어 속성개수 조회 도구
export const getLayerAttributesCount = tool({
  description: `레이어의 속성 데이터 개수를 조회합니다. 필터 조건을 적용하여 특정 조건에 맞는 데이터의 개수를 확인할 수 있습니다.`,
  parameters: z.object({
    typeName: z.string().describe("레이어 타입명 (예: Wgeontest3:L100000249)"),
    attributeFilter: z.string().optional().describe("속성 필터 내용 (예: \"구청명\" like '강%')"),
    spatialFilter: z.string().optional().describe("공간 필터 내용 (예: POLYGON((190000 540000, 200000 540000, 200000 550000, 190000 550000, 190000 540000)))"),
    spatialFilterSrid: z.string().optional().describe("공간 필터 SRID (예: 5186)")
  }),
  execute: async ({
    typeName,
    attributeFilter,
    spatialFilter,
    spatialFilterSrid
  }) => {
    try {
      const config = getApiConfig();

      const params = new URLSearchParams({
        typeName: typeName,
      });

      // 선택적 파라미터들 추가
      if (attributeFilter && attributeFilter.trim() !== "") {
        params.append("attributeFilter", attributeFilter.trim());
      }

      if (spatialFilter && spatialFilter.trim() !== "") {
        params.append("spatialFilter", spatialFilter.trim());
      }

      if (spatialFilterSrid && spatialFilterSrid.trim() !== "") {
        params.append("spatialFilterSrid", spatialFilterSrid.trim());
      }

      // 인증 정보 추가
      if (config.headers.crtfckey) {
        params.append("crtfckey", config.headers.crtfckey);
      }

      const response = await fetch(
        `${config.baseUrl}/layer/attributes/count?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            ...config.headers,
          },
        }
      );

      if (!response.ok) {
        response.json().then((data) => {
          console.error("API request failed with data", data);
        });
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = (await response.json()) as any;

      if (!data) {
        return { error: "레이어 속성개수 조회 실패: 응답 데이터가 없습니다." };
      }

      return {
        typeName,
        count: data.count || data.result || data,
        attributeFilter,
        spatialFilter,
        spatialFilterSrid,
        description: `레이어 ${typeName}의 속성 데이터 개수: ${data.count || data.result || data}개`
      };
    } catch (error: any) {
      return { error: `레이어 속성개수 조회 실패: ${error.message}` };
    }
  },
});

export const HILTools = {
  chooseOption,
  getUserInput,
  confirmWithCheckbox,
} as const;

export default HILTools;
