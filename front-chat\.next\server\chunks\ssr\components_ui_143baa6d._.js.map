{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/resizable.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { GripVertical } from \"lucide-react\"\r\nimport * as ResizablePrimitive from \"react-resizable-panels\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ResizablePanelGroup = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) => (\r\n  <ResizablePrimitive.PanelGroup\r\n    className={cn(\r\n      \"flex h-full w-full data-[panel-group-direction=vertical]:flex-col\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\n\r\nconst ResizablePanel = ResizablePrimitive.Panel\r\n\r\nconst ResizableHandle = ({\r\n  withHandle,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {\r\n  withHandle?: boolean\r\n}) => (\r\n  <ResizablePrimitive.PanelResizeHandle\r\n    className={cn(\r\n      \"relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {withHandle && (\r\n      <div className=\"z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border\">\r\n        <GripVertical className=\"h-2.5 w-2.5\" />\r\n      </div>\r\n    )}\r\n  </ResizablePrimitive.PanelResizeHandle>\r\n)\r\n\r\nexport { ResizablePanelGroup, ResizablePanel, ResizableHandle }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,sBAAsB,CAAC,EAC3B,SAAS,EACT,GAAG,OACwD,iBAC3D,uVAAC,2TAAA,CAAA,aAA6B;QAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qEACA;QAED,GAAG,KAAK;;;;;;AAIb,MAAM,iBAAiB,2TAAA,CAAA,QAAwB;AAE/C,MAAM,kBAAkB,CAAC,EACvB,UAAU,EACV,SAAS,EACT,GAAG,OAGJ,iBACC,uVAAC,2TAAA,CAAA,oBAAoC;QACnC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2oBACA;QAED,GAAG,KAAK;kBAER,4BACC,uVAAC;YAAI,WAAU;sBACb,cAAA,uVAAC,0SAAA,CAAA,eAAY;gBAAC,WAAU", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/hover-card.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst HoverCard = HoverCardPrimitive.Root\r\n\r\nconst HoverCardTrigger = HoverCardPrimitive.Trigger\r\n\r\nconst HoverCardContent = React.forwardRef<\r\n  React.ElementRef<typeof HoverCardPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <HoverCardPrimitive.Content\r\n    ref={ref}\r\n    align={align}\r\n    sideOffset={sideOffset}\r\n    className={cn(\r\n      \"z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nHoverCardContent.displayName = HoverCardPrimitive.Content.displayName\r\n\r\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,YAAY,mRAAA,CAAA,OAAuB;AAEzC,MAAM,mBAAmB,mRAAA,CAAA,UAA0B;AAEnD,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,uVAAC,mRAAA,CAAA,UAA0B;QACzB,KAAK;QACL,OAAO;QACP,YAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8aACA;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,mRAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n))\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n))\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,uVAAC,iRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,uVAAC,iRAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,uVAAC;;;;;0BACD,uVAAC,iRAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,iRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,uVAAC,iRAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,uVAAC,iRAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,iRAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,uVAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,uVAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,uVAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,uVAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,uVAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,uVAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,uVAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,uVAAC;;;;;8BACD,uVAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,uVAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,uVAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,uVAAC;gBAAK,WAAU;0BACd,cAAA,uVAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,uVAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,uVAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gRAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,gRAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gRAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,gRAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gRAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,gRAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gRAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,6QAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,6QAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,uVAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,uVAAC,6QAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,6QAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Slider = React.forwardRef<\r\n  React.ElementRef<typeof SliderPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SliderPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full touch-none select-none items-center\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\r\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\r\n    </SliderPrimitive.Track>\r\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\r\n  </SliderPrimitive.Root>\r\n))\r\nSlider.displayName = SliderPrimitive.Root.displayName\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,uVAAC,+QAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,uVAAC,+QAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,uVAAC,+QAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,uVAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/ai-reasoning-card.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { HoverCard, HoverCardContent, HoverCardTrigger } from \"@/components/ui/hover-card\";\r\nimport { MagicCard } from \"@/components/magicui/magic-card\";\r\nimport { BrainI<PERSON>, <PERSON>rkles } from \"lucide-react\";\r\n\r\ninterface AIReasoningCardProps {\r\n  children: React.ReactNode;\r\n  isEnabled: boolean;\r\n  isThinking?: boolean;\r\n  isDisabled?: boolean;\r\n  disabledReason?: string;\r\n  className?: string;\r\n}\r\n\r\nexport function AIReasoningCard({\r\n  children,\r\n  isEnabled,\r\n  isThinking = false,\r\n  isDisabled = false,\r\n  disabledReason,\r\n  className,\r\n}: AIReasoningCardProps) {\r\n\r\n  const content = (\r\n    <motion.div\r\n      className=\"space-y-4 w-72\"\r\n      initial={{ opacity: 0, y: 8 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ delay: 0.1, duration: 0.3, ease: \"easeOut\" }}\r\n    >\r\n      {/* Header with enhanced brain icon */}\r\n      <div className=\"flex items-center gap-3\">\r\n        <motion.div\r\n          className={cn(\r\n            \"relative p-2.5 rounded-xl transition-all duration-300\",\r\n            isDisabled\r\n              ? \"bg-gray-50 text-gray-300 border border-gray-200 opacity-50\"\r\n              : isEnabled\r\n                ? \"bg-gradient-to-br from-blue-50 to-indigo-50 text-blue-600 shadow-sm border border-blue-100\"\r\n                : \"bg-gray-50 text-gray-400 border border-gray-200\"\r\n          )}\r\n          animate={isEnabled && isThinking && !isDisabled ? {\r\n            scale: [1, 1.05, 1],\r\n          } : {}}\r\n          transition={{\r\n            duration: 2,\r\n            repeat: isEnabled && isThinking && !isDisabled ? Infinity : 0,\r\n            ease: \"easeInOut\",\r\n          }}\r\n        >\r\n          <BrainIcon className=\"w-5 h-5\" />\r\n          <motion.div\r\n            className=\"absolute -top-1 -right-1\"\r\n            animate={{\r\n              scale: [0, 1, 0],\r\n              opacity: [0, 1, 0],\r\n            }}\r\n            transition={{\r\n              duration: 1.5,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\",\r\n            }}\r\n          >\r\n            <Sparkles className=\"w-3 h-3 text-blue-500\" />\r\n          </motion.div>\r\n        </motion.div>\r\n\r\n        <div className=\"flex-1\">\r\n          <h4 className={cn(\r\n            \"text-sm font-semibold transition-colors\",\r\n            isDisabled\r\n              ? \"text-gray-400\"\r\n              : isEnabled\r\n                ? \"text-gray-900\"\r\n                : \"text-gray-500\"\r\n          )}>\r\n            AI 추론 {isDisabled ? \"지원 안됨\" : isEnabled ? \"활성화\" : \"비활성화\"}\r\n          </h4>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Feature highlights */}\r\n      <div className=\"space-y-2\">\r\n        {isDisabled ? (\r\n          <>\r\n            {disabledReason && (\r\n              <div className=\"flex items-center gap-2 text-xs\">\r\n                <div className=\"w-1.5 h-1.5 rounded-full bg-gray-300\" />\r\n                <span className=\"text-gray-400\">\r\n                  {disabledReason}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </>\r\n        ) : (\r\n          <>\r\n            <div className=\"flex items-center gap-2 text-xs\">\r\n              <div className={cn(\r\n                \"w-1.5 h-1.5 rounded-full\",\r\n                isEnabled ? \"bg-blue-500\" : \"bg-gray-300\"\r\n              )} />\r\n              <span className={cn(\r\n                \"transition-colors\",\r\n                isEnabled ? \"text-gray-700\" : \"text-gray-400\"\r\n              )}>\r\n                추론을 통해 좀 더 정확한 답변을 제공\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2 text-xs\">\r\n              <div className={cn(\r\n                \"w-1.5 h-1.5 rounded-full\",\r\n                isEnabled ? \"bg-blue-500\" : \"bg-gray-300\"\r\n              )} />\r\n              <span className={cn(\r\n                \"transition-colors\",\r\n                isEnabled ? \"text-gray-700\" : \"text-gray-400\"\r\n              )}>\r\n                시간이 좀 더 소요될 수 있습니다.\r\n              </span>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n\r\n  return (\r\n    <HoverCard openDelay={150} closeDelay={200}>\r\n      <HoverCardTrigger asChild>\r\n        {children}\r\n      </HoverCardTrigger>\r\n      <HoverCardContent\r\n        side=\"top\"\r\n        align=\"center\"\r\n        className=\"p-0 border-0 bg-transparent shadow-none z-[100]\"\r\n        sideOffset={12}\r\n        alignOffset={-50}\r\n        avoidCollisions={true}\r\n        collisionBoundary={typeof document !== 'undefined' ? document.body : undefined}\r\n      >\r\n\r\n        <MagicCard\r\n          className={cn(\r\n            \"border-0 shadow-xl backdrop-blur-md p-4 overflow-hidden\",\r\n            isEnabled\r\n              ? \"bg-gradient-to-br from-white/95 to-blue-50/95\"\r\n              : \"bg-white/95\"\r\n          )}\r\n          gradientSize={120}\r\n          gradientColor={isEnabled ? \"#3b82f6\" : \"#6b7280\"}\r\n          gradientOpacity={isEnabled ? 0.15 : 0.08}\r\n          gradientFrom={isEnabled ? \"#3b82f6\" : \"#6b7280\"}\r\n          gradientTo={isEnabled ? \"#6366f1\" : \"#9ca3af\"}\r\n        >\r\n          {content}\r\n        </MagicCard>\r\n      </HoverCardContent>\r\n    </HoverCard>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAPA;;;;;;;AAkBO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,aAAa,KAAK,EAClB,aAAa,KAAK,EAClB,cAAc,EACd,SAAS,EACY;IAErB,MAAM,wBACJ,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;YAAK,UAAU;YAAK,MAAM;QAAU;;0BAGzD,uVAAC;gBAAI,WAAU;;kCACb,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA,aACI,+DACA,YACE,+FACA;wBAER,SAAS,aAAa,cAAc,CAAC,aAAa;4BAChD,OAAO;gCAAC;gCAAG;gCAAM;6BAAE;wBACrB,IAAI,CAAC;wBACL,YAAY;4BACV,UAAU;4BACV,QAAQ,aAAa,cAAc,CAAC,aAAa,WAAW;4BAC5D,MAAM;wBACR;;0CAEA,uVAAC,4RAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,uVAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,OAAO;wCAAC;wCAAG;wCAAG;qCAAE;oCAChB,SAAS;wCAAC;wCAAG;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;0CAEA,cAAA,uVAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,uVAAC;wBAAI,WAAU;kCACb,cAAA,uVAAC;4BAAG,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACd,2CACA,aACI,kBACA,YACE,kBACA;;gCACL;gCACM,aAAa,UAAU,YAAY,QAAQ;;;;;;;;;;;;;;;;;;0BAMxD,uVAAC;gBAAI,WAAU;0BACZ,2BACC;8BACG,gCACC,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAI,WAAU;;;;;;0CACf,uVAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;kDAMT;;sCACE,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,4BACA,YAAY,gBAAgB;;;;;;8CAE9B,uVAAC;oCAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAChB,qBACA,YAAY,kBAAkB;8CAC7B;;;;;;;;;;;;sCAIL,uVAAC;4BAAI,WAAU;;8CACb,uVAAC;oCAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,4BACA,YAAY,gBAAgB;;;;;;8CAE9B,uVAAC;oCAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAChB,qBACA,YAAY,kBAAkB;8CAC7B;;;;;;;;;;;;;;;;;;;;;;;;;IAUf,qBACE,uVAAC,kIAAA,CAAA,YAAS;QAAC,WAAW;QAAK,YAAY;;0BACrC,uVAAC,kIAAA,CAAA,mBAAgB;gBAAC,OAAO;0BACtB;;;;;;0BAEH,uVAAC,kIAAA,CAAA,mBAAgB;gBACf,MAAK;gBACL,OAAM;gBACN,WAAU;gBACV,YAAY;gBACZ,aAAa,CAAC;gBACd,iBAAiB;gBACjB,mBAAmB,OAAO,aAAa,cAAc,SAAS,IAAI,GAAG;0BAGrE,cAAA,uVAAC,uIAAA,CAAA,YAAS;oBACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA,YACI,kDACA;oBAEN,cAAc;oBACd,eAAe,YAAY,YAAY;oBACvC,iBAAiB,YAAY,OAAO;oBACpC,cAAc,YAAY,YAAY;oBACtC,YAAY,YAAY,YAAY;8BAEnC;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDown } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Accordion = AccordionPrimitive.Root\r\n\r\nconst AccordionItem = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\r\n>(({ className, ...props }, ref) => (\r\n  <AccordionPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\"border-b\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAccordionItem.displayName = \"AccordionItem\"\r\n\r\nconst AccordionTrigger = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Header className=\"flex\">\r\n    <AccordionPrimitive.Trigger\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\r\n    </AccordionPrimitive.Trigger>\r\n  </AccordionPrimitive.Header>\r\n))\r\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\r\n\r\nconst AccordionContent = React.forwardRef<\r\n  React.ElementRef<typeof AccordionPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Content\r\n    ref={ref}\r\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\r\n    {...props}\r\n  >\r\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\r\n  </AccordionPrimitive.Content>\r\n))\r\n\r\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,4QAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,4QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,uVAAC,4QAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,uVAAC,4QAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,uVAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,4QAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,uVAAC,4QAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,uVAAC;YAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,4QAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,uVAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,uVAAC,wRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,8SAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/ui/server-data-table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport {\n  ColumnDef,\n  ColumnFiltersState,\n  SortingState,\n  VisibilityState,\n  flexRender,\n  getCoreRowModel,\n  getFilteredRowModel,\n  getSortedRowModel,\n  useReactTable,\n} from \"@tanstack/react-table\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Input } from \"@/components/ui/input\"\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"@/components/ui/table\"\nimport { ScrollArea, ScrollBar } from \"@/components/ui/scroll-area\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\n\ninterface ServerDataTableProps<TData, TValue> {\n  columns: ColumnDef<TData, TValue>[]\n  data: TData[]\n  searchKey?: string\n  searchPlaceholder?: string\n  // 서버 사이드 페이지네이션 props\n  pageIndex: number\n  pageSize: number\n  totalCount: number\n  onPageChange: (pageIndex: number) => void\n  onPageSizeChange: (pageSize: number) => void\n  isLoading?: boolean\n}\n\nexport function ServerDataTable<TData, TValue>({\n  columns,\n  data,\n  searchKey,\n  searchPlaceholder = \"검색...\",\n  pageIndex,\n  pageSize,\n  totalCount,\n  onPageChange,\n  onPageSizeChange,\n  isLoading = false,\n}: ServerDataTableProps<TData, TValue>) {\n  const [sorting, setSorting] = React.useState<SortingState>([])\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])\n  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})\n\n  const table = useReactTable({\n    data,\n    columns,\n    onSortingChange: setSorting,\n    onColumnFiltersChange: setColumnFilters,\n    getCoreRowModel: getCoreRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n    getFilteredRowModel: getFilteredRowModel(),\n    onColumnVisibilityChange: setColumnVisibility,\n    manualPagination: true, // 서버 사이드 페이지네이션 활성화\n    pageCount: Math.ceil(totalCount / pageSize), // 전체 페이지 수 계산\n    state: {\n      sorting,\n      columnFilters,\n      columnVisibility,\n      pagination: {\n        pageIndex: pageIndex - 1, // 0-based index로 변환\n        pageSize,\n      },\n    },\n  })\n\n  // 페이지 변경 핸들러\n  const handlePageChange = (newPageIndex: number) => {\n    onPageChange(newPageIndex + 1) // 1-based index로 변환\n  }\n\n  // 페이지 크기 변경 핸들러\n  const handlePageSizeChange = (newPageSize: string) => {\n    onPageSizeChange(Number(newPageSize))\n  }\n\n  // 페이지네이션 정보 계산\n  const totalPages = Math.ceil(totalCount / pageSize)\n  const startItem = (pageIndex - 1) * pageSize + 1\n  const endItem = Math.min(pageIndex * pageSize, totalCount)\n\n  return (\n    <div className=\"flex flex-col h-full overflow-hidden\">\n      {/* 검색 및 컬럼 선택 - 고정 영역 */}\n      <div className=\"flex items-center py-3 flex-shrink-0 border-b bg-background\">\n        {searchKey && (\n          <Input\n            placeholder={searchPlaceholder}\n            value={(table.getColumn(searchKey)?.getFilterValue() as string) ?? \"\"}\n            onChange={(event) =>\n              table.getColumn(searchKey)?.setFilterValue(event.target.value)\n            }\n            className=\"max-w-sm h-8\"\n          />\n        )}\n\n        {/* 페이지 크기 선택 */}\n        <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>\n          <SelectTrigger className=\"ml-2 h-8 w-32\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"25\">25개씩 보기</SelectItem>\n            <SelectItem value=\"50\">50개씩 보기</SelectItem>\n            <SelectItem value=\"100\">100개씩 보기</SelectItem>\n            <SelectItem value=\"200\">200개씩 보기</SelectItem>\n          </SelectContent>\n        </Select>\n\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"outline\" size=\"sm\" className=\"ml-auto h-8\">\n              컬럼 <ChevronDown className=\"ml-2 h-3 w-3\" />\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\">\n            {table\n              .getAllColumns()\n              .filter((column) => column.getCanHide())\n              .map((column) => {\n                return (\n                  <DropdownMenuCheckboxItem\n                    key={column.id}\n                    className=\"capitalize\"\n                    checked={column.getIsVisible()}\n                    onCheckedChange={(value) =>\n                      column.toggleVisibility(!!value)\n                    }\n                  >\n                    {column.id}\n                  </DropdownMenuCheckboxItem>\n                )\n              })}\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n\n      {/* 테이블 컨테이너 - 고정 높이 + 강제 스크롤 */}\n      <div className=\"flex-1 min-h-0 border rounded-md overflow-hidden bg-background\">\n        <ScrollArea className=\"h-full w-full\">\n          <div style={{ minHeight: '400px', maxHeight: '500px', height: '100%' }}>\n            <Table className=\"w-full\">\n              {/* 고정 헤더 */}\n              <TableHeader>\n                {table.getHeaderGroups().map((headerGroup) => (\n                  <TableRow key={headerGroup.id} className=\"hover:bg-transparent border-b\">\n                    {headerGroup.headers.map((header) => (\n                      <TableHead\n                        key={header.id}\n                        className=\"sticky top-0 z-10 bg-muted border-r last:border-r-0 min-w-[120px] p-2 h-auto\"\n                      >\n                        {header.isPlaceholder\n                          ? null\n                          : flexRender(header.column.columnDef.header, header.getContext())}\n                      </TableHead>\n                    ))}\n                  </TableRow>\n                ))}\n              </TableHeader>\n\n              {/* 스크롤 가능한 바디 */}\n              <TableBody>\n                {isLoading ? (\n                  <TableRow>\n                    <TableCell\n                      colSpan={columns.length}\n                      className=\"h-24 text-center text-muted-foreground\"\n                    >\n                      데이터를 불러오는 중...\n                    </TableCell>\n                  </TableRow>\n                ) : table.getRowModel().rows?.length ? (\n                  table.getRowModel().rows.map((row) => (\n                    <TableRow key={row.id} className=\"hover:bg-muted/30 border-b\">\n                      {row.getVisibleCells().map((cell) => (\n                        <TableCell\n                          key={cell.id}\n                          className=\"border-r last:border-r-0 min-w-[120px] p-2 text-sm whitespace-nowrap\"\n                        >\n                          {flexRender(\n                            cell.column.columnDef.cell,\n                            cell.getContext()\n                          )}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  ))\n                ) : (\n                  <TableRow>\n                    <TableCell\n                      colSpan={columns.length}\n                      className=\"h-24 text-center text-muted-foreground\"\n                    >\n                      데이터가 없습니다.\n                    </TableCell>\n                  </TableRow>\n                )}\n              </TableBody>\n            </Table>\n          </div>\n          <ScrollBar orientation=\"vertical\" />\n          <ScrollBar orientation=\"horizontal\" />\n        </ScrollArea>\n      </div>\n\n      {/* 서버 사이드 페이지네이션 - 고정 영역 */}\n      <div className=\"flex items-center justify-between space-x-2 py-3 flex-shrink-0 border-t bg-background\">\n        <div className=\"flex-1 text-sm text-muted-foreground\">\n          총 {totalCount.toLocaleString()}개 항목 중 {startItem.toLocaleString()}-{endItem.toLocaleString()}개 표시\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"text-sm text-muted-foreground\">\n            페이지 {pageIndex} / {totalPages}\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handlePageChange(pageIndex - 2)} // 0-based로 변환하여 전달\n            disabled={pageIndex <= 1 || isLoading}\n          >\n            이전\n          </Button>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handlePageChange(pageIndex)} // 0-based로 변환하여 전달\n            disabled={pageIndex >= totalPages || isLoading}\n          >\n            다음\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAWA;AAEA;AACA;AAMA;AACA;AAQA;AACA;AAjCA;;;;;;;;;;;AAuDO,SAAS,gBAA+B,EAC7C,OAAO,EACP,IAAI,EACJ,SAAS,EACT,oBAAoB,OAAO,EAC3B,SAAS,EACT,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,gBAAgB,EAChB,YAAY,KAAK,EACmB;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAsB,EAAE;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAc,AAAD,EAAmB,CAAC;IAEjF,MAAM,QAAQ,CAAA,GAAA,gSAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD;QAC/B,mBAAmB,CAAA,GAAA,8OAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,8OAAA,CAAA,sBAAmB,AAAD;QACvC,0BAA0B;QAC1B,kBAAkB;QAClB,WAAW,KAAK,IAAI,CAAC,aAAa;QAClC,OAAO;YACL;YACA;YACA;YACA,YAAY;gBACV,WAAW,YAAY;gBACvB;YACF;QACF;IACF;IAEA,aAAa;IACb,MAAM,mBAAmB,CAAC;QACxB,aAAa,eAAe,GAAG,oBAAoB;;IACrD;IAEA,gBAAgB;IAChB,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB,OAAO;IAC1B;IAEA,eAAe;IACf,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAC1C,MAAM,YAAY,CAAC,YAAY,CAAC,IAAI,WAAW;IAC/C,MAAM,UAAU,KAAK,GAAG,CAAC,YAAY,UAAU;IAE/C,qBACE,uVAAC;QAAI,WAAU;;0BAEb,uVAAC;gBAAI,WAAU;;oBACZ,2BACC,uVAAC,0HAAA,CAAA,QAAK;wBACJ,aAAa;wBACb,OAAO,AAAC,MAAM,SAAS,CAAC,YAAY,oBAA+B;wBACnE,UAAU,CAAC,QACT,MAAM,SAAS,CAAC,YAAY,eAAe,MAAM,MAAM,CAAC,KAAK;wBAE/D,WAAU;;;;;;kCAKd,uVAAC,2HAAA,CAAA,SAAM;wBAAC,OAAO,SAAS,QAAQ;wBAAI,eAAe;;0CACjD,uVAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,uVAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0CAEd,uVAAC,2HAAA,CAAA,gBAAa;;kDACZ,uVAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,uVAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,uVAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,uVAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;;;;;;;;;;;;;kCAI5B,uVAAC,qIAAA,CAAA,eAAY;;0CACX,uVAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,uVAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;wCAAc;sDACvD,uVAAC,wSAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG9B,uVAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAM;0CACxB,MACE,aAAa,GACb,MAAM,CAAC,CAAC,SAAW,OAAO,UAAU,IACpC,GAAG,CAAC,CAAC;oCACJ,qBACE,uVAAC,qIAAA,CAAA,2BAAwB;wCAEvB,WAAU;wCACV,SAAS,OAAO,YAAY;wCAC5B,iBAAiB,CAAC,QAChB,OAAO,gBAAgB,CAAC,CAAC,CAAC;kDAG3B,OAAO,EAAE;uCAPL,OAAO,EAAE;;;;;gCAUpB;;;;;;;;;;;;;;;;;;0BAMR,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,uVAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAS,WAAW;gCAAS,QAAQ;4BAAO;sCACnE,cAAA,uVAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;;kDAEf,uVAAC,0HAAA,CAAA,cAAW;kDACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,uVAAC,0HAAA,CAAA,WAAQ;gDAAsB,WAAU;0DACtC,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,uVAAC,0HAAA,CAAA,YAAS;wDAER,WAAU;kEAET,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,UAAU;uDAL3D,OAAO,EAAE;;;;;+CAHL,YAAY,EAAE;;;;;;;;;;kDAgBjC,uVAAC,0HAAA,CAAA,YAAS;kDACP,0BACC,uVAAC,0HAAA,CAAA,WAAQ;sDACP,cAAA,uVAAC,0HAAA,CAAA,YAAS;gDACR,SAAS,QAAQ,MAAM;gDACvB,WAAU;0DACX;;;;;;;;;;mDAID,MAAM,WAAW,GAAG,IAAI,EAAE,SAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC5B,uVAAC,0HAAA,CAAA,WAAQ;gDAAc,WAAU;0DAC9B,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,uVAAC,0HAAA,CAAA,YAAS;wDAER,WAAU;kEAET,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uDALZ,KAAK,EAAE;;;;;+CAHH,IAAI,EAAE;;;;sEAevB,uVAAC,0HAAA,CAAA,WAAQ;sDACP,cAAA,uVAAC,0HAAA,CAAA,YAAS;gDACR,SAAS,QAAQ,MAAM;gDACvB,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQX,uVAAC,mIAAA,CAAA,YAAS;4BAAC,aAAY;;;;;;sCACvB,uVAAC,mIAAA,CAAA,YAAS;4BAAC,aAAY;;;;;;;;;;;;;;;;;0BAK3B,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;;4BAAuC;4BACjD,WAAW,cAAc;4BAAG;4BAAQ,UAAU,cAAc;4BAAG;4BAAE,QAAQ,cAAc;4BAAG;;;;;;;kCAE/F,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAI,WAAU;;oCAAgC;oCACxC;oCAAU;oCAAI;;;;;;;0CAErB,uVAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,iBAAiB,YAAY;gCAC5C,UAAU,aAAa,KAAK;0CAC7B;;;;;;0CAGD,uVAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,iBAAiB;gCAChC,UAAU,aAAa,cAAc;0CACtC;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}