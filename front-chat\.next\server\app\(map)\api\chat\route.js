"use strict";(()=>{var e={};e.id=222,e.ids=[222],e.modules={1997:(e,t,r)=>{let a;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{arrayBufferToString:function(){return s},decrypt:function(){return u},encrypt:function(){return c},getActionEncryptionKey:function(){return y},getClientReferenceManifestForRsc:function(){return m},getServerModuleMap:function(){return g},setReferenceManifestsSingleton:function(){return p},stringToUint8Array:function(){return l}});let o=r(61882),n=r(96451),i=r(29294);function s(e){let t=new Uint8Array(e),r=t.byteLength;if(r<65535)return String.fromCharCode.apply(null,t);let a="";for(let e=0;e<r;e++)a+=String.fromCharCode(t[e]);return a}function l(e){let t=e.length,r=new Uint8Array(t);for(let a=0;a<t;a++)r[a]=e.charCodeAt(a);return r}function c(e,t,r){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,r)}function u(e,t,r){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,r)}let d=Symbol.for("next.server.action-manifests");function p({page:e,clientReferenceManifest:t,serverActionsManifest:r,serverModuleMap:a}){var o;let i=null==(o=globalThis[d])?void 0:o.clientReferenceManifestsPerPage;globalThis[d]={clientReferenceManifestsPerPage:{...i,[(0,n.normalizeAppPath)(e)]:t},serverActionsManifest:r,serverModuleMap:a}}function g(){let e=globalThis[d];if(!e)throw Object.defineProperty(new o.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function m(){let e=globalThis[d];if(!e)throw Object.defineProperty(new o.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,r=i.workAsyncStorage.getStore();if(!r){var a=t;let e=Object.values(a),r={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)r.clientModules={...r.clientModules,...t.clientModules},r.edgeRscModuleMapping={...r.edgeRscModuleMapping,...t.edgeRscModuleMapping},r.rscModuleMapping={...r.rscModuleMapping,...t.rscModuleMapping};return r}let n=t[r.route];if(!n)throw Object.defineProperty(new o.InvariantError(`Missing Client Reference Manifest for ${r.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return n}async function y(){if(a)return a;let e=globalThis[d];if(!e)throw Object.defineProperty(new o.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new o.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return a=await crypto.subtle.importKey("raw",l(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15058:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>ea,routeModule:()=>Z,serverHooks:()=>er,workAsyncStorage:()=>ee,workUnitAsyncStorage:()=>et});var a={};r.r(a),r.d(a,{DELETE:()=>J,POST:()=>Q,maxDuration:()=>H});var o=r(81160),n=r(18765),i=r(46332),s=r(88668),l=r(34112),c=r(47841),u=r(28538),d=r(75077),p=r(85457),g=r(61401),m=r(70669);function y(e){let t=e.toLowerCase();for(let[e,r]of Object.entries({노후화된:"건축년도나 노후화",오래된:"건축년도나 사용연한",높은:"높이나 층수",큰:"규모나 면적",작은:"규모나 면적",새로운:"건축년도나 준공년도",최신:"건축년도나 준공년도"}))if(t.includes(e))return r;return"특정 조건"}function f(e){let t=e.toLowerCase();for(let e of["흰색","노란색","빨간색","파란색","초록색","검은색"])if(t.includes(e))return e;return null}let A={LAYER_ADD:{priority:4,keywords:["추가","보여줘","찾아서","검색해서"],description:"레이어 추가",attributePatterns:["노후화된","오래된","낡은","높은","큰","작은","새로운","최신","~년도 이후","~년도 이전","~이상","~이하","~보다"],colorPatterns:["흰색","노란색","빨간색","파란색","초록색","검은색"]}};var b=r(94062),h=r(25613),L=r(16387);let E={eMapBasic:"바로e맵 일반지도",eMapAIR:"바로e맵 항공지도",eMapColor:"바로e맵 색각지도",eMapWhite:"바로e맵 백지도"};({...m.qc,experimental_toToolResultContent:e=>{let t=e.features?.length||0,r="FeatureCollection"===e.type?"공간 밀도 분석":"밀도 분석";return[{type:"text",text:`${r}이 완료되었습니다. 총 ${t}개의 공간 데이터를 분석하여 밀도 분포를 계산했습니다. 분석 결과가 지도에 히트맵 형태로 시각화되었습니다.`}]}});let I={...m.pr,experimental_toToolResultContent:e=>{if(!e.result?.jusoList?.length)return[{type:"text",text:"검색 결과가 없습니다. 다른 키워드로 다시 검색해보세요."}];let t=e.result.jusoList,r=t.length,a=t.slice(0,3).map(e=>({roadAddr:e.roadAddr,buildName:e.buildName||e.poiName,buildLo:e.buildLo,buildLa:e.buildLa})),o=`주소 검색이 완료되었습니다. 총 ${r}개의 위치를 찾았습니다.`,n=a.map((e,t)=>`${t+1}. ${e.roadAddr}${e.buildName?` (${e.buildName})`:""} - 좌표: ${e.buildLo},${e.buildLa}`).join("\n");return r>3&&(n+=`
... 외 ${r-3}개 추가 결과`),[{type:"text",text:`${o}

${n}`}]}},R={...m.pr,description:"출발지 위치를 검색합니다. 경로 탐색의 시작점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",experimental_toToolResultContent:e=>{if(!e.result?.jusoList?.length)return[{type:"text",text:"출발지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요."}];let t=e.result.jusoList,r=t.length,a=t.slice(0,3).map(e=>({roadAddr:e.roadAddr,buildName:e.buildName||e.poiName,buildLo:e.buildLo,buildLa:e.buildLa})),o=`출발지 검색이 완료되었습니다. 총 ${r}개의 위치를 찾았습니다.`,n=a.map((e,t)=>`${t+1}. ${e.roadAddr}${e.buildName?` (${e.buildName})`:""} - 좌표: ${e.buildLo},${e.buildLa}`).join("\n");return r>3&&(n+=`
... 외 ${r-3}개 추가 결과`),[{type:"text",text:`${o}

${n}`}]}},_={...m.pr,description:"목적지 위치를 검색합니다. 경로 탐색의 도착점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",experimental_toToolResultContent:e=>{if(!e.result?.jusoList?.length)return[{type:"text",text:"목적지를 찾을 수 없습니다. 다른 키워드로 다시 검색해보세요."}];let t=e.result.jusoList,r=t.length,a=t.slice(0,3).map(e=>({roadAddr:e.roadAddr,buildName:e.buildName||e.poiName,buildLo:e.buildLo,buildLa:e.buildLa})),o=`목적지 검색이 완료되었습니다. 총 ${r}개의 위치를 찾았습니다.`,n=a.map((e,t)=>`${t+1}. ${e.roadAddr}${e.buildName?` (${e.buildName})`:""} - 좌표: ${e.buildLo},${e.buildLa}`).join("\n");return r>3&&(n+=`
... 외 ${r-3}개 추가 결과`),[{type:"text",text:`${o}

${n}`}]}},N={...m.Jl,execute:async(e,t)=>{let{avoid:r,...a}=e;return console.log("searchDirections - avoid 파라미터 제거됨:",r),m.Jl.execute(a,t)},experimental_toToolResultContent:e=>{if(e.error)return[{type:"text",text:`경로 탐색 실패: ${e.error}`}];if(!e.routes||!e.routes.length)return[{type:"text",text:"경로를 찾을 수 없습니다. 출발지와 목적지를 확인해주세요."}];let t=e.routes[0];if(0!==t.result_code)return[{type:"text",text:`경로 탐색 실패: ${t.result_msg||"알 수 없는 오류가 발생했습니다."}`}];let r=t.summary,a=r?.distance?`${(r.distance/1e3).toFixed(1)}km`:"정보 없음",o=r?.duration?`${Math.floor(r.duration/60)}분`:"정보 없음",n=r?.fare?.taxi?`${r.fare.taxi.toLocaleString()}원`:"정보 없음",i=r?.fare?.toll?`${r.fare.toll.toLocaleString()}원`:"무료",s=e.origin?.name||"출발지",l=e.destination?.name||"목적지";return[{type:"text",text:`경로 탐색이 완료되었습니다.

📍 ${s} → ${l}
🚗 거리: ${a}
⏱️ 소요시간: ${o}
💰 예상 택시요금: ${n}
🛣️ 통행료: ${i}

경로가 지도에 자동으로 표시되었습니다.`}]}};(0,s.z6)({description:`사용 가능한 배경지도 목록을 조회합니다.
  반환되는 배경지도 목록:
  - eMapBasic: 바로e맵 일반지도 (도로, 건물, 지형지물 포함)
  - eMapAIR: 바로e맵 항공지도 (위성 항공사진)
  - eMapColor: 바로e맵 색각지도 (색상 대비 강화)
  - eMapWhite: 바로e맵 백지도 (깔끔한 흰색 배경)`,parameters:h.z.object({}),execute:async()=>({basemaps:Object.entries(E).map(([e,t])=>({id:e,name:t,displayName:t}))})});let S=(0,s.z6)({description:`배경지도를 변경합니다.
  사용 가능한 배경지도 ID:
  - eMapBasic: 일반지도 (기본 지도)
  - eMapAIR: 항공지도 (위성지도)
  - eMapColor: 색각지도 (색상 대비)
  - eMapWhite: 백지도 (심플한 배경)

  키워드 매칭:
  - "위성지도", "항공지도" → eMapAIR
  - "일반지도", "기본지도" → eMapBasic
  - "색각지도", "컬러지도" → eMapColor
  - "백지도", "흰지도" → eMapWhite`,parameters:h.z.object({basemapId:h.z.enum(["eMapBasic","eMapAIR","eMapColor","eMapWhite"]).describe("변경할 배경지도의 ID (eMapBasic, eMapAIR, eMapColor, eMapWhite 중 하나)")}),execute:async({basemapId:e})=>{let t=E[e];return t?{basemap:e,basemapId:e,basemapName:t,success:!0,message:`배경지도가 ${t}로 변경되었습니다.`}:{error:`지원하지 않는 배경지도 ID입니다: ${e}`,availableBasemaps:Object.keys(E)}}});(0,s.z6)({description:`지도의 중심점을 특정 좌표로 이동합니다.

  사용 예시:
  - 특정 좌표로 이동: "지도를 127.027926, 37.497175로 이동"
  - 상대적 이동: "지도를 동쪽으로 500m 이동", "북쪽으로 1km 이동"
  - 방향별 이동: "위로", "아래로", "왼쪽으로", "오른쪽으로"`,parameters:h.z.object({longitude:h.z.number().describe("경도 (X좌표)"),latitude:h.z.number().describe("위도 (Y좌표)"),moveType:h.z.enum(["absolute","relative"]).describe("이동 타입: absolute(절대좌표), relative(상대이동)").optional().default("absolute"),description:h.z.string().describe("이동에 대한 설명").optional()}),execute:async({longitude:e,latitude:t,moveType:r,description:a})=>{try{return{success:!0,center:[e,t],moveType:r,message:a||`지도 중심점이 경도 ${e}, 위도 ${t}로 이동되었습니다.`}}catch(e){return{success:!1,error:`지도 중심점 이동 실패: ${e.message}`}}}});let T=(0,s.z6)({description:`지도의 확대/축소 레벨을 조정합니다.

  확대/축소 레벨:
  - 1-5: 국가/대륙 레벨 (매우 넓은 범위)
  - 6-10: 지역/도시 레벨 (넓은 범위)
  - 11-15: 구/동 레벨 (중간 범위)
  - 16-20: 건물/도로 레벨 (상세 범위)

  사용 예시:
  - "지도 확대해줘" → zoomDirection: "in", zoomLevel: 2, zoomType: "relative"
  - "더 넓게 보여줘" → zoomDirection: "out", zoomLevel: 2, zoomType: "relative"
  - "최대한 확대" → zoomLevel: 18, zoomType: "absolute"
  - "전체 보기" → zoomLevel: 2, zoomType: "absolute"
`,parameters:h.z.object({zoomLevel:h.z.number().optional().describe("확대/축소 레벨 또는 변경량 (1-20)"),zoomType:h.z.enum(["absolute","relative"]).describe("확대/축소 타입: absolute(절대레벨), relative(상대변경)").optional().default("relative"),zoomDirection:h.z.enum(["in","out"]).describe("확대/축소 방향: in(확대), out(축소)"),description:h.z.string().describe("확대/축소에 대한 설명").optional()}),execute:async({zoomLevel:e=2,zoomType:t,zoomDirection:r,description:a})=>{try{return{success:!0,zoom:e,zoomType:t,zoomDirection:r,message:a||`지도 ${"in"===r?"확대":"축소"} 레벨이 ${"relative"===t?"변경":"설정"}되었습니다.`}}catch(e){return{success:!1,error:`지도 확대/축소 실패: ${e.message}`}}}}),O=(0,s.z6)({description:`지도를 특정 방향으로 이동합니다.

  지원하는 방향:
  - north, up, 위, 북쪽: 북쪽으로 이동
  - south, down, 아래, 남쪽: 남쪽으로 이동
  - east, right, 오른쪽, 동쪽: 동쪽으로 이동
  - west, left, 왼쪽, 서쪽: 서쪽으로 이동

  거리 형식:
  - "500m", "1km", "2000m" 등의 형태로 입력
  - 단위가 없으면 미터(m)로 간주
  - 기본값: "500m"

  좌표계 지원:
  - EPSG:5186 (Korea 2000 / Central Belt 2010): 미터 단위 직접 계산
  - 정확한 거리 이동을 위해 투영 좌표계 사용`,parameters:h.z.object({direction:h.z.enum(["north","south","east","west","up","down","left","right"]).describe("이동 방향"),distance:h.z.string().describe("이동 거리 (예: '500m', '1km', '2000m')").optional().default("500m")}),execute:async({direction:e,distance:t})=>{try{let r=(e=>{let t=e.match(/^(\d+(?:\.\d+)?)\s*(m|km)?$/i);if(!t)throw Error(`잘못된 거리 형식: ${e}`);let r=parseFloat(t[1]),a=t[2]?.toLowerCase()||"m";return"km"===a?1e3*r:r})(t);console.log(`moveMapByDirection: ${e}, ${t} -> ${r}m`);let a=0,o=0;switch(e){case"north":case"up":o=r;break;case"south":case"down":o=-r;break;case"east":case"right":a=r;break;case"west":case"left":a=-r}return{success:!0,direction:e,deltaX:a,deltaY:o,distance:r,coordinateSystem:"EPSG:5186",message:`지도가 ${({north:"북쪽",south:"남쪽",east:"동쪽",west:"서쪽",up:"위쪽",down:"아래쪽",left:"왼쪽",right:"오른쪽"})[e]}으로 ${t} 이동되었습니다.`}}catch(e){return{success:!1,error:`지도 방향 이동 실패: ${e.message}`}}}}),w=(0,s.z6)({description:"사용자에게 여러 선택지 중 하나를 고르도록 요청합니다. 결과는 options 배열의 값 중 하나입니다.",parameters:h.z.object({message:h.z.string().describe("사용자에게 보여줄 안내 문구"),options:h.z.array(h.z.string().describe("사용자에게 표시될 텍스트. 반드시 'key|value' 형태로 제공하세요.")).describe("사용자가 선택할 수 있는 옵션 문자열 배열")})}),v=(0,s.z6)({description:`레이어 목록을 검색합니다.
**레이어 타입 매핑 (lyrTySeCode): 명시하지 않는 경우 반드시 값을 비워둡니다**
`,parameters:h.z.object({userId:h.z.string().describe("사용자 ID"),layerName:h.z.string().optional().describe("레이어 이름"),lyrTySeCode:h.z.string().optional().describe("레이어 유형 코드 ('1': 점, '2': 선, '3': 면, 비어있는 경우 전체)"),holdDataSeCode:h.z.string().optional().describe("데이터 구분 코드 ('0': 전체, '1': 사용자, '2': 공유, '9': 국가 (기본값 '0'))"),pageIndex:h.z.string().optional().describe("조회할 페이지 인덱스 (기본값 '1')"),pageSize:h.z.string().optional().describe("한 페이지당 조회할 레이어 수 (기본값 '10')")}),async execute({userId:e,layerName:t="",lyrTySeCode:r="",holdDataSeCode:a="0",pageIndex:o="1",pageSize:n="10"}){try{let e=(0,L.Jh)(),i=new URLSearchParams({userId:(0,L.nK)(e),holdDataSeCode:a,pageIndex:o,pageSize:n});t&&""!==t.trim()&&i.append("searchTxt",t.trim()),r&&""!==r.trim()&&i.append("lyrTySeCode",r.trim()),e.headers.crtfckey&&i.append("crtfckey",e.headers.crtfckey);let s=await fetch(`${e.baseUrl}/smt/layer/info/list?${i.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...e.headers}});if(!s.ok)throw s.json().then(e=>{console.error("API request failed with data",e)}),Error(`API request failed with status ${s.status}`);let l=await s.json();if(!l||!l.result)return{error:"레이어 목록 조회 실패: 응답 데이터가 없습니다."};return l}catch(e){return{error:`레이어 목록 조회 실패: ${e.message}`}}}}),$=(0,s.z6)({description:`레이어의 시각적 스타일을 변경합니다. 레이어 타입(점/선/면)에 따라 적절한 스타일이 적용됩니다.

**지원하는 스타일 속성:**
- color: 기본 색상 (hex 코드, 예: "#FF0000") - 모든 타입
- fillOpacity: 채우기 투명도 (0.0-1.0) - 점, 면 타입
- strokeColor: 윤곽선 색상 (hex 코드) - 모든 타입
- strokeWidth: 윤곽선 두께 (픽셀) - 모든 타입
- radius: 점 크기 (픽셀) - 점 타입만
- width: 선 두께 (픽셀) - 선 타입만
- symbol: 심볼 타입 (점 타입만) - "circle", "square", "triangle", "star", "cross", "x"

**레이어 타입별 적용:**
- 점(Point) 레이어: color, fillOpacity, strokeColor, strokeWidth, radius, symbol
- 선(Line) 레이어: color, strokeColor, strokeWidth, width
- 면(Polygon) 레이어: color, fillOpacity, strokeColor(윤곽선), strokeWidth(윤곽선)

**사용 예시:**
- "빨간색으로 바꿔줘" → color: "#FF0000"
- "투명하게 해줘" → fillOpacity: 0.3
- "윤곽선을 두껍게" → strokeWidth: 3
- "크기를 키워줘" → radius: 10 (점) 또는 width: 5 (선)
- "별 모양으로 바꿔줘" → symbol: "star"
- "사각형으로 바꿔줘" → symbol: "square"
- "십자 모양으로 해줘" → symbol: "cross"`,parameters:h.z.object({layerId:h.z.string().describe("스타일을 변경할 레이어 ID"),color:h.z.string().optional().describe("기본 색상 (hex 코드, 예: #FF0000)"),fillOpacity:h.z.number().min(0).max(1).optional().describe("채우기 투명도 (0.0-1.0)"),strokeColor:h.z.string().optional().describe("윤곽선 색상 (hex 코드)"),strokeWidth:h.z.number().min(0).max(20).optional().describe("윤곽선 두께 (픽셀)"),radius:h.z.number().min(1).max(50).optional().describe("점 크기 (픽셀, 점 타입만)"),width:h.z.number().min(1).max(20).optional().describe("선 두께 (픽셀, 선 타입만)"),symbol:h.z.enum(["circle","square","triangle","star","cross","x"]).optional().describe("심볼 타입 (점 타입만): circle(원), square(사각형), triangle(삼각형), star(별), cross(십자), x(X자)"),description:h.z.string().optional().describe("변경 사항에 대한 설명")}),async execute({layerId:e,color:t,fillOpacity:r,strokeColor:a,strokeWidth:o,radius:n,width:i,symbol:s,description:l}){try{let c={};return t&&(c.color=t),void 0!==r&&(c.fillOpacity=r),a&&(c.strokeColor=a),void 0!==o&&(c.strokeWidth=o),void 0!==n&&(c.radius=n),void 0!==i&&(c.width=i),s&&(c.symbol=s),{layerId:e,styleUpdate:c,description:l||"레이어 스타일이 업데이트되었습니다.",success:!0}}catch(e){return{error:`스타일 업데이트 실패: ${e.message}`,success:!1}}}}),D=(0,s.z6)({description:`레이어에 여러 조건별 스타일을 적용합니다. 사용자가 "A는 빨간색, B는 파란색" 같은 요청을 할 때 사용합니다.
attributes 매개변수로 filter 조건에 사용할 수 있는 속성 정보를 제공해야 합니다.
예시: 
`,parameters:h.z.object({layerId:h.z.string().describe("스타일을 적용할 레이어 ID"),lyrNm:h.z.string().describe("레이어이름"),attributes:h.z.string().describe("\uD83D\uDEA8 필수: 속성명과 설명을 모두 포함한 형태로 제공 \uD83D\uDEA8 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요"),userInstruction:h.z.string().describe("사용자의 자연어 스타일링 요청 (예: '용산구는 노란색, 강남구는 파란색, 나머지는 회색으로', '벽돌구조이면서 1990년도 이전인 건물은 빨간색으로')")}),async execute({layerId:e,lyrNm:t,attributes:r,userInstruction:a}){try{console.log("generateCategoricalStyle called with:",{layerId:e,lyrNm:t,attributes:r,userInstruction:a});let{object:o}=await (0,s.pY)({model:(0,b.N)("gpt-4.1-nano",{structuredOutputs:!0}),schema:h.z.object({styleRules:h.z.array(h.z.object({description:h.z.string().describe("규칙 설명"),color:h.z.string().describe("HEX 색상 코드 (예: #FF0000, #0000FF, #00FF00)"),conditions:h.z.array(h.z.object({attributeName:h.z.string().describe("속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용"),condition:h.z.enum(["like","equal","greater","less","greaterEqual","lessEqual","default"]).describe("조건 타입"),value:h.z.string().describe("조건 값")})).describe("조건 배열 (복수 조건 지원). default 조건인 경우 빈 배열"),logicalOperator:h.z.enum(["AND","OR"]).describe("복수 조건 간 논리 연산자 (기본값: AND)")}))}),prompt:`다음 자연어 스타일링 요청을 분석해서 스타일 규칙으로 변환해주세요:

**사용자 요청**: "${a}"
**레이어명**: "${t}"
**사용 가능한 속성들**: "${r}"

**🚨 중요 제약사항 🚨**:
- **반드시 위의 "사용 가능한 속성들"에서 속성명을 추출하여 사용하세요**
- 속성 형식: "a17(건폐율), a13(사용승인일자)" → 속성명은 "a17", "a13" 사용
- 속성 형식: "c1(주소), c2(건물명)" → 속성명은 "c1", "c2" 사용
- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**
- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요

**변환 규칙**:
1. 자연어에서 조건과 색상을 추출하여 스타일 규칙 생성
2. 색상은 HEX 코드로 직접 생성 (예: #FF0000, #0000FF, #00FF00, #FFFF00, #800080, #FFA500, #FFC0CB, #A52A2A, #808080, #000000, #FFFFFF)
3. **속성명 선택 규칙**:
   - 사용자 요청을 분석하여 위의 "사용 가능한 속성들" 목록에서만 적절한 속성을 선택
   - 목록에 없는 속성은 절대 사용하지 마세요
   - **복합 조건**: 여러 속성을 조합하여 사용 가능 (단, 모두 위 목록에 있는 속성만)
4. **🚨 복합 조건 처리 규칙 🚨**:
   - **"그리고", "이면서", "동시에", "또한"**: 하나의 규칙으로 처리, conditions 배열에 여러 조건 포함
   - **"또는", "이거나"**: 하나의 규칙으로 처리, logicalOperator를 "OR"로 설정
   - **"A는 X색, B는 Y색"**: 별개의 규칙들로 분리
   - **단일 조건**: conditions 배열에 하나의 조건만 포함
5. 조건 타입:
   - "like": 텍스트 포함 검색 (기본값, 대부분의 경우)
   - "equal": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)
   - "greater": 숫자 > 비교 (명시적으로 "초과", "보다 큰" 등이 언급된 경우)
   - "less": 숫자 < 비교 (명시적으로 "미만", "보다 작은" 등이 언급된 경우)
   - "greaterEqual": 숫자 >= 비교 (명시적으로 "이상" 등이 언급된 경우)
   - "lessEqual": 숫자 <= 비교 (명시적으로 "이하" 등이 언급된 경우)
   - "default": 기본/나머지 스타일 (조건 없음)

**변환 예시** (속성명 추출 방법):

**🚨 중요: "그리고", "이면서", "동시에" 등은 하나의 규칙으로 처리! 🚨**

**속성명 추출 방법:**
- 속성 정보: "a17(건폐율), a13(사용승인일자)" → 사용할 속성명: "a17", "a13"
- 속성 정보: "c1(주소), c2(건물명)" → 사용할 속성명: "c1", "c2"
- 속성 정보: "정제지번주소" → 사용할 속성명: "정제지번주소"

**단일 조건:**
- "서울에 있는 데이터만 파란색으로" (속성: "정제지번주소") →
  [{"description": "서울 지역 - 파란색", "color": "#0000FF", "conditions": [{"attributeName": "정제지번주소", "condition": "like", "value": "서울"}], "logicalOperator": "AND"}]

**복합 조건 (하나의 규칙):**
- "건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물" (속성: "a17(건폐율), a13(사용승인일자)") →
  [{"description": "건폐율 50 이상이고 사용승인일자가 1980년 이전인 건물 - 빨간색", "color": "#FF0000", "conditions": [{"attributeName": "a17", "condition": "greaterEqual", "value": "50"}, {"attributeName": "a13", "condition": "less", "value": "1980"}], "logicalOperator": "AND"}]

**여러 규칙 (별개 조건):**
- "대학은 파란색, 중학교는 빨간색으로" (속성: 교육기관타입) →
  [
    {"description": "대학 - 파란색", "color": "#0000FF", "conditions": [{"attributeName": "교육기관타입", "condition": "like", "value": "대학"}], "logicalOperator": "AND"},
    {"description": "중학교 - 빨간색", "color": "#FF0000", "conditions": [{"attributeName": "교육기관타입", "condition": "like", "value": "중학교"}], "logicalOperator": "AND"}
  ]

- "나머지/기본 스타일" →
  [{"description": "나머지 - 회색", "color": "#808080", "conditions": [], "logicalOperator": "AND"}]

**중요**:
- 특별한 언급이 없으면 condition은 "like" 사용
- **logicalOperator는 항상 포함**: 단일 조건이어도 "AND" 명시, 복합 조건은 "AND" 또는 "OR" 명시
- **항상 default 규칙을 포함**: 사용자가 특정 조건만 언급해도 나머지 데이터를 위한 기본 스타일(회색 #808080)을 자동으로 추가
- 사용자가 "나머지는 X색으로" 명시한 경우에만 해당 색상 사용, 그렇지 않으면 기본 회색 사용
- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`});console.log("Processed style rules:",o.styleRules);let n=o.styleRules.map(e=>({...e,logicalOperator:e.logicalOperator||"AND"})),i=n.some(e=>0===e.conditions.length||e.conditions.some(e=>"default"===e.condition)),l=[...n];return i||(l.push({description:"나머지 - 기본 스타일",color:"#808080",conditions:[],logicalOperator:"AND"}),console.log("Added default rule automatically")),{layerId:e,attributes:r,styleRules:l,description:`${l.length}개 유형별 스타일 적용 (${r.length}개 속성 활용)`,success:!0,type:"categorical"}}catch(e){return{error:`유형별 스타일 생성 실패: ${e.message}`,success:!1}}}}),C=(0,s.z6)({description:`지도에서 레이어를 삭제합니다. 삭제된 레이어는 복구할 수 없으므로 신중하게 사용하세요.

**사용 방법:**
- layerId: 삭제할 레이어의 ID를 정확히 입력하세요
- Current map state에서 레이어 ID를 확인할 수 있습니다

**사용 예시:**
- "스타벅스 레이어를 삭제해줘" → layerId: "LR0000001234"
- "이 레이어를 지워줘" → 현재 활성 레이어의 ID 사용
- "모든 레이어를 삭제해줘" → 각 레이어 ID를 순차적으로 삭제`,parameters:h.z.object({layerId:h.z.string().describe("삭제할 레이어의 ID"),description:h.z.string().optional().describe("삭제 사유 또는 설명")}),async execute({layerId:e,description:t}){try{return console.log("removeLayer called with layerId:",e),console.log("description:",t),{layerId:e,description:t||"레이어가 삭제되었습니다.",success:!0}}catch(e){return{error:`레이어 삭제 실패: ${e.message}`,success:!1}}}}),M=(0,s.z6)({description:`레이어의 속성정보를 기반으로 CQL 필터를 생성합니다. 단일 조건과 복수 조건을 모두 지원합니다.

  필드 선택 가이드:
  - getLayerAttributes 도구로 조회한 properties의 필드명을 사용하세요
  - 필드명은 정확히 일치해야 합니다 (대소문자 구분)

  연산자 사용 가이드:
  - 텍스트: LIKE (부분일치), = (완전일치)
  - 숫자: >, >=, <, <=, =
  - 목록: IN

  복수 조건 지원:
  - AND: 모든 조건이 참이어야 함
  - OR: 하나 이상의 조건이 참이면 됨
  - 예시: "서울이면서 인구 100만 이상", "강남구 또는 서초구"

  Args:
    lyr_id: 필터 적용할 레이어 ID
    attributes: 사용 가능한 속성 정보 (형식: "a17(건폐율), a13(사용승인일자)")
    user_instruction: 자연어 필터링 요청 (예: "서울이면서 인구 100만 이상", "강남구 또는 서초구")
     `,parameters:h.z.object({lyrId:h.z.string().min(1).describe("필터를 적용할 레이어 ID"),attributes:h.z.string().describe("\uD83D\uDEA8 필수: 속성명과 설명을 모두 포함한 형태로 제공 \uD83D\uDEA8 - 형식: 'a17(건폐율), a13(사용승인일자)' 또는 'c1(주소), c2(건물명)' - 절대 'a17'처럼 속성명만 제공하지 마세요"),userInstruction:h.z.string().describe("사용자의 자연어 필터링 요청 (예: '서울이면서 인구 100만 이상', '강남구 또는 서초구', '건폐율 50 이상')")}),execute:async({lyrId:e,attributes:t,userInstruction:r})=>{try{console.log("createLayerFilter called with:",{lyrId:e,attributes:t,userInstruction:r});let{object:a}=await (0,s.pY)({model:(0,b.N)("gpt-4.1-nano",{structuredOutputs:!0}),schema:h.z.object({conditions:h.z.array(h.z.object({attributeName:h.z.string().describe("속성명 - 반드시 attributes 매개변수에서 제공된 속성명만 사용"),operator:h.z.enum(["=",">","<",">=","<=","LIKE","IN"]).describe("비교 연산자"),value:h.z.string().describe("조건 값")})).describe("필터 조건 배열"),logicalOperator:h.z.enum(["AND","OR"]).describe("복수 조건 간 논리 연산자 (기본값: AND)")}),prompt:`다음 자연어 필터링 요청을 분석해서 CQL 필터 조건으로 변환해주세요:

**사용자 요청**: "${r}"
**사용 가능한 속성들**: "${t}"

**🚨 중요 제약사항 🚨**:
- **반드시 위의 "사용 가능한 속성들"에서 속성명을 추출하여 사용하세요**
- 속성 형식: "a17(건폐율), a13(사용승인일자)" → 속성명은 "a17", "a13" 사용
- 속성 형식: "c1(주소), c2(건물명)" → 속성명은 "c1", "c2" 사용
- **괄호 안의 설명은 무시하고 괄호 앞의 속성명만 사용하세요**
- 존재하지 않는 가상의 속성명을 절대 만들어내지 마세요

**변환 규칙**:
1. 자연어에서 조건을 추출하여 필터 조건 생성
2. **속성명 선택 규칙**:
   - 사용자 요청을 분석하여 위의 "사용 가능한 속성들" 목록에서만 적절한 속성을 선택
   - 목록에 없는 속성은 절대 사용하지 마세요
3. **논리 연산자 처리 규칙**:
   - **"그리고", "이면서", "동시에", "또한"**: logicalOperator를 "AND"로 설정
   - **"또는", "이거나"**: logicalOperator를 "OR"로 설정
   - **단일 조건**: logicalOperator를 "AND"로 설정
4. 연산자 타입:
   - "LIKE": 텍스트 포함 검색 (기본값, 대부분의 경우)
   - "=": 정확한 값 매칭 (특별히 정확한 매칭이 필요한 경우만)
   - ">": 숫자 > 비교 (명시적으로 "초과", "보다 큰" 등이 언급된 경우)
   - "<": 숫자 < 비교 (명시적으로 "미만", "보다 작은" 등이 언급된 경우)
   - ">=": 숫자 >= 비교 (명시적으로 "이상" 등이 언급된 경우)
   - "<=": 숫자 <= 비교 (명시적으로 "이하" 등이 언급된 경우)
   - "IN": 목록 값 매칭 (여러 값 중 하나와 일치)

**변환 예시**:
- "서울에 있는 데이터만" (속성: "정제지번주소") →
  {"conditions": [{"attributeName": "정제지번주소", "operator": "LIKE", "value": "서울"}], "logicalOperator": "AND"}

- "건폐율 50 이상이고 사용승인일자가 1980년 이전" (속성: "a17(건폐율), a13(사용승인일자)") →
  {"conditions": [{"attributeName": "a17", "operator": ">=", "value": "50"}, {"attributeName": "a13", "operator": "<", "value": "1980"}], "logicalOperator": "AND"}

- "강남구 또는 서초구" (속성: "구명") →
  {"conditions": [{"attributeName": "구명", "operator": "LIKE", "value": "강남구"}, {"attributeName": "구명", "operator": "LIKE", "value": "서초구"}], "logicalOperator": "OR"}

**중요**:
- 특별한 언급이 없으면 operator는 "LIKE" 사용
- **logicalOperator는 항상 포함**: 단일 조건이어도 "AND" 명시
- **다시 한번 강조: 반드시 제공된 속성 목록에서만 속성명을 선택하세요**`});console.log("Processed filter conditions:",a);let o="";if(0===a.conditions.length)return{error:"필터 조건이 생성되지 않았습니다."};if(1===a.conditions.length){let e=a.conditions[0];o=z(e)}else{let e=a.conditions.map(e=>z(e)),t=a.logicalOperator||"AND";o=e.join(` ${t} `)}return{lyr_id:e,filter:o,description:`${a.conditions.length}개 조건으로 필터링: ${r}`,conditions:a.conditions,logicalOperator:a.logicalOperator}}catch(e){return{error:`필터 생성 실패: ${e.message}`}}}});function z(e){let{attributeName:t,operator:r,value:a}=e,o=t.trim().replace(/['"]/g,"");if("LIKE"===r.toUpperCase())return`"${o}" LIKE '%${a}%'`;if("IN"===r.toUpperCase()){let e=a.split(",").map(e=>e.trim()).map(e=>`'${e}'`).join(",");return`"${o}" IN (${e})`}{let e=!isNaN(Number(a));return`"${o}"${r}${e?a:`'${a}'`}`}}let x=(0,s.z6)({description:`레이어의 속성 데이터 개수를 조회합니다. 필터 조건을 적용하여 특정 조건에 맞는 데이터의 개수를 확인할 수 있습니다.`,parameters:h.z.object({typeName:h.z.string().describe("레이어 타입명 (예: Wgeontest3:L100000249)"),attributeFilter:h.z.string().optional().describe("속성 필터 내용 (예: \"구청명\" like '강%')"),spatialFilter:h.z.string().optional().describe("공간 필터 내용 (예: POLYGON((190000 540000, 200000 540000, 200000 550000, 190000 550000, 190000 540000)))"),spatialFilterSrid:h.z.string().optional().describe("공간 필터 SRID (예: 5186)")}),execute:async({typeName:e,attributeFilter:t,spatialFilter:r,spatialFilterSrid:a})=>{try{let o=(0,L.Jh)(),n=new URLSearchParams({typeName:e});t&&""!==t.trim()&&n.append("attributeFilter",t.trim()),r&&""!==r.trim()&&n.append("spatialFilter",r.trim()),a&&""!==a.trim()&&n.append("spatialFilterSrid",a.trim()),o.headers.crtfckey&&n.append("crtfckey",o.headers.crtfckey);let i=await fetch(`${o.baseUrl}/layer/attributes/count?${n.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",...o.headers}});if(!i.ok)throw i.json().then(e=>{console.error("API request failed with data",e)}),Error(`API request failed with status ${i.status}`);let s=await i.json();if(!s)return{error:"레이어 속성개수 조회 실패: 응답 데이터가 없습니다."};return{typeName:e,count:s.count||s.result||s,attributeFilter:t,spatialFilter:r,spatialFilterSrid:a,description:`레이어 ${e}의 속성 데이터 개수: ${s.count||s.result||s}개`}}catch(e){return{error:`레이어 속성개수 조회 실패: ${e.message}`}}}}),k={chooseOption:w,getUserInput:m.OW,confirmWithCheckbox:m.OV},P={MAP_STATE_CHECK:"모든 작업 전에 Current map state에서 대상 레이어 존재 여부 확인",LAYER_ID_FORMAT:"LR로 시작하는 10자리 (예: LR0000003825)",LAYER_ID_EXTRACTION:"chooseOption 결과가 '레이어명|레이어ID' 형태면 '|' 뒤의 ID 추출",DUPLICATE_PREVENTION:"이미 지도에 있는 레이어는 getLayer 재호출 금지",WORK_ORDER:"현재 지도 상태 확인 → 레이어 ID 확보 → 필요시에만 도구 호출",HIL_USAGE:"검색 결과 여러개: chooseOption (반드시 '레이어명|레이어ID' 형태), 검색 실패: getUserInput, 중요 작업: confirmWithCheckbox, 사용자 위치 확인: getLocation",ATTRIBUTE_REQUEST_PATTERNS:`
    형용사: "노후화된", "높은", "오래된", "새로운", "큰", "작은", "낡은", "최신"
    필터링: "~만", "~인 것만", "조건에 맞는", "특정 조건"
    색상 지정: "흰색으로", "노란색으로", "빨간색으로", "파란색으로"
    비교: "~보다 높은", "~이상", "~이하", "~년도 이후", "~년도 이전"
    지역 조건: "서울에 있는", "부산에 있는", "강남구의", "용산구만", "경기도에 있는", "~시의", "~구의", "~동의"`},j={getLayerList:"userId='geonuser', layerName='키워드' lyrTySeCode='' (비워둠)",getLayer:"현재 지도 상태 확인 후 중복 방지, 레이어 ID 형식 검증",getLayerAttributes:`
    - getLayer 선행 필수, 400 에러 시 getLayer 재시도
    - 속성 기반 요청 시 반드시 호출 (노후화, 높이, 크기 등)
    - 조건부 스타일링 전 필수 단계
    - 속성 컬럼 정보를 바탕으로 사용자 조건 매칭
    - 🚨 지역 조건 시 우선순위: 시도명 > 주소 > 구명 > 동명 > 기타 지역 관련 컬럼`,updateLayerStyle:"현재 지도 상태 확인, 유형별 스타일링 시 속성 조회 필수",removeLayer:"현재 지도 상태 확인, 중요 작업 시 confirmWithCheckbox 사용"},F={ADD_LAYER:"지도 상태 확인 → ID 확보 → 필요시 검색/선택 → getLayer",CHANGE_STYLE:"지도 상태 확인 → 레이어 없으면 추가 → 스타일 적용",DELETE_LAYER:"지도 상태 확인 → ID 확인 → 필요시 확인 → removeLayer",LAYER_WITH_ATTRIBUTES:`
    1. getLayerList로 키워드 검색
    2. 검색 결과 처리 (chooseOption/자동선택)
    3. getLayer로 레이어 추가
    4. getLayerAttributes로 속성 정보 조회
    5. 필요시 조건부 스타일링 또는 필터링 적용`,CONDITIONAL_STYLING:`
    1. 현재 지도 상태에서 대상 레이어 확인
    2. 레이어가 없으면 LAYER_WITH_ATTRIBUTES 워크플로우 실행
    3. getLayerAttributes로 속성 정보 조회 (필수)
    4. 속성값 기반 조건부 스타일링 적용`,ATTRIBUTE_BASED_FILTER:`
    1. 대상 레이어 확보 (없으면 검색/추가)
    2. getLayerAttributes로 속성 컬럼 확인
    3. 사용자 조건에 맞는 속성 컬럼 식별
    4. createLayerFilter 또는 updateLayerStyle로 필터링 적용`},U={NEARBY_POI_SEARCH:{keywords:["근처","주변","이 근처","여기 근처","주위","인근","nearby","around here","close to"],description:"주변/근처 POI 검색",examples:["여기 근처에 맛집","주변 카페","이 근처 병원"],alternative:"구체적인 장소명 검색 (예: '강남역 스타벅스')"},CATEGORY_LIST_REQUEST:{keywords:["전체 목록","모든 ~","~ 목록","~ 리스트"],description:"카테고리별 전체 목록 요청",examples:["맛집 목록","카페 목록","병원 목록"],alternative:"구체적인 브랜드명 검색 (예: '스타벅스', '이디야')",exceptions:["레이어 목록","사용가능한 레이어","데이터 종류"]},REALTIME_INFO:{keywords:["실시간","지금","현재","실시간 교통","교통상황","정체","실시간 날씨","현재 날씨","실시간 위치"],description:"실시간 정보 요청",examples:["실시간 교통상황","지금 날씨","현재 교통정보"],alternative:"정적 데이터 기반 길찾기 및 레이어 정보"},DATA_MANAGEMENT:{keywords:["업로드","올리기","파일 추가","편집","수정","삭제","다운로드","저장","내보내기","export","새로 만들기","생성"],description:"데이터 관리 기능",examples:["데이터 업로드","파일 편집","레이어 생성"],alternative:"기본 제공 레이어 활용 및 검색"},ADVANCED_SPATIAL_ANALYSIS:{keywords:["버퍼","반경","buffer","radius","오버레이","overlay","중첩","네트워크 분석","공간분석","spatial analysis"],description:"고급 공간분석",examples:["500m 버퍼 분석","오버레이 분석","네트워크 분석"],alternative:"밀도 분석 기능",exceptions:["밀도 분석","밀도"]},ADVANCED_MAP_FEATURES:{keywords:["3D","삼차원","입체","애니메이션","시간대별","temporal","사용자 정의 심볼","커스텀 스타일","회전","돌려줘","각도","기울여","틸트","rotate","tilt"],description:"고급 지도 기능",examples:["3D 지도","시간대별 애니메이션","커스텀 심볼","지도 회전","지도 기울기"],alternative:"기본 2D 지도, 표준 스타일링, 지도 이동 및 확대/축소"}};U.NEARBY_POI_SEARCH,U.CATEGORY_LIST_REQUEST,U.REALTIME_INFO,U.DATA_MANAGEMENT,U.CATEGORY_LIST_REQUEST,U.ADVANCED_SPATIAL_ANALYSIS,U.ADVANCED_SPATIAL_ANALYSIS,U.DATA_MANAGEMENT,U.ADVANCED_MAP_FEATURES,U.DATA_MANAGEMENT;let B={language:`응답은 한국어로 작성하세요.`,tone:`친근하고 자연스러운 톤으로 사용자에게 도움을 제공하세요.`,interaction:`명확한 지시는 즉시 실행하고, 정말 애매한 경우에만 사용자 입력 도구를 사용하세요. 이미 동의한 작업을 다시 확인하지 마세요.`,coreRules:`
🚨🚨🚨 **모든 에이전트 공통 핵심 규칙** 🚨🚨🚨
1. **컨텍스트 유지**: 대화 맥락을 파악하여 지시사항을 준수하세요.
2. **정확한 파라미터**: 키워드가 없으면 빈 값으로 설정
`},Y=h.z.object({intent:h.z.enum(["LAYER_ADD","LAYER_REMOVE","LAYER_STYLE","LAYER_FILTER","LAYER_LIST","NAVIGATION","MAP_CONTROL","BASEMAP_CHANGE","DENSITY_ANALYSIS","GENERAL_CONVERSATION","UNSUPPORTED_FEATURE","UNSURE"]).describe("분석된 사용자의 핵심 의도 카테고리"),message:h.z.string().describe("다음 에이전트에게 전달할 구체적인 작업 지시 메시지 (시스템 내부 통신용)")}),G={tools:{getLayerList:v},system:`
    ${B.language}
    ${B.tone}
    ${B.interaction}

    **중요: 이전 대화 컨텍스트 분석**
    - 이전 어시스턴트 메시지에서 도구 호출 결과(getLayerList, chooseOption 등)를 확인하세요
    - 현재 워크플로우 단계를 파악하고 다음 단계에 맞는 구체적인 지시를 생성하세요
    - 레이어가 이미 선택된 상태라면 속성 조회 및 필터링 단계로 진행하도록 지시하세요

    ${`
당신은 사용자 요청을 분석하여 다음 중 하나로 분류합니다:

**의도 카테고리:**
- LAYER_*: 레이어 관련 작업 (추가/삭제/스타일/필터)
- MAP_*: 지도 조작 (확대/축소/이동/배경지도)
- NAVIGATION: 장소 검색/길찾기
- DENSITY_ANALYSIS: 밀도 분석 (점 타입 레이어만 가능)
- UNSUPPORTED_FEATURE: 지원 불가 기능
- GENERAL_CONVERSATION: 일반 대화

**지원 불가 키워드:** "근처", "주변", "실시간", "업로드", "전체목록", "CSV", "파일"

**워크플로우 상태 인식:**
- 이전 대화에서 getLayerList, chooseOption 등의 도구 호출 결과를 확인하세요
- 레이어가 이미 선택된 상태인지, 아직 검색 단계인지 파악하세요
- 현재 워크플로우 단계에 맞는 구체적인 다음 작업을 지시하세요

**속성 기반 레이어 요청 식별:**
- 형용사: "노후화된", "높은", "오래된", "새로운", "큰", "작은"
- 색상 지정: "흰색으로", "노란색으로", "빨간색으로"
- 이런 패턴이 감지되면 LAYER_ADD로 분류하고 구체적인 필터링 지시 생성

**응답 형식:** {"intent": "CATEGORY", "message": "작업 지시"}

**메시지 작성 원칙:**
- **레이어 선택 완료 + 속성 조건**: "선택한 레이어를 추가하고, X 기준으로 표시해야 합니다. 속성 정보를 조회하고 사용자에게 확인을 요청하세요."
- **속성 기반 요청 (초기)**: "X 관련 레이어를 검색하고, Y 기준에 따라 필터링하여 Z로 표시해주세요"
- **일반 레이어 요청**: "X 레이어를 검색하여 지도에 추가하겠습니다"
- 구체적인 작업 단계와 도구 사용 계획 제시
- 제약사항 명시 (예: 밀도분석은 점 타입만 가능)

사용자 요청을 분석하여 적절한 의도로 분류하고 다음 에이전트를 위한 명확한 작업 지시를 생성하세요.
  `.trim()}
  `,maxSteps:1,outputSchema:Y},q={intent_analyzer:G,navigation:{tools:{searchAddress:I,searchOrigin:R,searchDestination:_,searchDirections:N,getLocation:m.g$,...k},system:`
      ${B.language}
      ${B.tone}
      ${B.interaction}
      당신은 위치 검색 및 길찾기 전문가입니다. 사용자의 요청을 받으면 **즉시 해당 도구를 호출**하여 작업을 수행합니다.

      **🚨 재시도 시 중요 규칙:**
      - 이전에 searchOrigin/searchDestination을 성공적으로 호출했다면, 그 결과를 재사용하세요
      - 동일한 장소를 다시 검색할 필요 없이, 이전 검색 결과의 좌표를 직접 사용하세요
      - 예: 이전에 "웨이버스" 검색 성공 → 재시도 시 동일한 좌표 사용

      **🚨 절대 지원하지 않는 기능 - 도구 호출 금지 🚨:**

      **1. 주변/근처 POI 검색 (절대 불가능):**
      - "여기 근처에 맛집", "주변 카페", "이 근처 병원", "근처 편의점" 등
      - **중요**: 어떤 장소명을 제공받아도 "주변" 검색은 불가능
      - **절대 searchAddress나 다른 도구를 호출하지 마세요**

      **2. 카테고리별 POI 목록 (절대 불가능):**
      - "맛집 목록", "카페 목록", "병원 목록" 등
      - **절대 도구 호출하지 마세요**

      **🚨 이런 요청 시 반드시 이렇게 응답하세요:**
      "죄송하지만 주변 POI 검색 기능은 현재 지원하지 않습니다. 저는 다음 기능만 도와드릴 수 있어요:
      1. 구체적인 장소명 검색 (예: '강남역 찾아줘')
      2. 두 지점 간 길찾기 (예: '강남역에서 홍대까지 가는 길')
      3. 특정 주소 검색

      구체적인 장소명을 알려주시면 해당 위치를 찾아드릴 수 있습니다."

      **경로찾기 패턴 인식 (최우선):**
      다음 패턴들은 **반드시 searchDirections 도구를 호출**해야 합니다:
      - "A에서 B까지" / "A부터 B까지" / "A → B" / "A에서 B로"
      - "가는 길" / "길찾기" / "경로" / "루트" / "네비게이션"
      - "어떻게 가" / "어떻게 이동" / "방법" (목적지 포함시)
      - "여기서 ~까지" / "현재 위치에서 ~까지"
      - 예시: "웨이버스에서 평촌역까지", "여기서 서울역 가는 길", "강남역에서 홍대 어떻게 가?"

      **필수 작업 절차:**

      0.  **🚨 지원되지 않는 기능 요청 확인 (최우선) 🚨**:
          **패턴 감지 키워드:**
          - "근처", "주변", "이 근처", "여기 근처" + POI 카테고리
          - POI 카테고리: "맛집", "카페", "병원", "편의점", "마트", "약국", "은행" 등

          **이런 요청 시 절대 규칙:**
          - **어떤 도구도 호출하지 마세요 (searchAddress, getLocation 등 모두 금지)**
          - **즉시 제한사항 안내 메시지만 출력**
          - **"해당 위치에서 검색해드릴 수 있습니다" 같은 잘못된 안내 금지**

      1.  **경로찾기 요청 (최우선 처리)**:
          → 출발지 인식: "여기서"/"현재 위치" → getLocation 호출
          → 출발지 인식: 구체적 장소명 → searchOrigin 호출
          → 도착지 인식: 항상 searchDestination 호출
          → **반드시 searchDirections 도구 호출**하여 경로 검색
          → 경로 결과를 사용자에게 제공

      **searchDirections 도구 호출 시 필수 좌표 형식:**

      ✅ **정확한 형식 예시:**
      - origin: "127.111202,37.394912" (경도,위도)
      - destination: "127.111202,37.394912" (경도,위도)

      **좌표 추출 방법:**
      1. searchOrigin/searchDestination 결과에서 buildLo(경도), buildLa(위도) 값 사용
      2. 형식: "{buildLo},{buildLa}" 또는 "{buildLo},{buildLa},name={buildName}"
      3. **반드시 실제 검색 결과의 좌표를 사용하세요** (예시 좌표 사용 금지)

      **🚨 searchDirections 호출 시 중요 규칙:**
      - avoid 파라미터는 생략하세요 (기본값 사용)
      - 잘못된 avoid 값 사용 시 API 오류 발생

      2.  **단순 위치 이동 요청** ("웨이버스로 이동해줘", "강남역 보여줘"):
          → **즉시 'searchAddress' 도구 호출**하여 장소 검색
          → **🎯 중요**: searchAddress 도구 호출 시 **자동으로 지도가 첫 번째 검색 결과로 이동**됩니다
          → 검색 결과가 여러 개면 'chooseOption' 도구로 사용자 선택 유도
          → 지도 이동이 완료되었음을 사용자에게 안내

      3.  **주변 시설 검색** ("근처 카페", "주변 맛집"):
          → **즉시 'searchAddress' 도구 호출**하여 검색
          → 여러 결과시 'chooseOption' 도구로 선택 유도

      **중요 규칙:**
      - 설명만 하지 말고 **반드시 도구를 호출**하세요
      - 경로찾기 패턴 감지시 → **무조건 searchDirections 호출**
      - "이동해줘", "보여줘", "찾아줘" → searchAddress 즉시 호출
      - **searchAddress 호출 시**: 지도가 자동으로 첫 번째 결과 위치로 이동됨
      - 검색 결과 여러 개 → chooseOption 즉시 호출
      - 지도 이동 완료 후 사용자에게 "위치를 찾았습니다" 등의 완료 메시지 제공

      사용자 요청을 받으면 즉시 적절한 도구를 호출하여 결과를 제공하세요.
    `},map_control:{tools:{changeBasemap:S,setMapZoom:T,moveMapByDirection:O,getLocation:m.g$,...k},system:`
      ${B.language}
      ${B.tone}
      당신은 지도 제어 전문가입니다. 배경지도 변경, 지도 확대/축소, 중심점 이동 등 **지원되는 지도 제어 기능만** 담당합니다.

      **🚨 최우선 규칙: 명확한 지도 조작 요청은 "~할까요?" 같은 확인 질문 없이 즉시 도구 호출! 🚨**

      **절대 금지 사항:**
      - "확대를 진행할까요?", "위성지도로 변경할까요?" 같은 확인 질문
      - 명확한 키워드가 있는 요청에 대한 추가 설명이나 확인
      - 지원되는 기능에 대한 사전 안내나 선택 옵션 제공

      **🚨 절대 규칙: 지원되는 기능만 도구 호출! 지원되지 않는 기능은 명확히 안내! 🚨**

      **🚨 지원되지 않는 기능 (절대 도구 호출 금지):**
      - **지도 회전**: "회전해줘", "돌려줘", "각도 변경" 등
      - **지도 기울기**: "기울여줘", "3D 뷰", "틸트" 등

      현재 지원하는 지도 제어 기능:
      - 배경지도 변경 (일반지도, 위성지도, 색각지도, 백지도)
      - 지도 확대/축소 (레벨 1-20)
      - 지도 이동 (북쪽, 남쪽, 동쪽, 서쪽으로 특정 거리)

      이 중에서 도움이 될 만한 기능이 있으시면 말씀해 주세요!"

      **지원하는 지도 제어 기능:**

      **1. 배경지도 변경 (changeBasemap):**
      - eMapBasic: 일반지도 (기본 지도)
      - eMapAIR: 항공지도 (위성지도)
      - eMapColor: 색각지도 (색상 대비)
      - eMapWhite: 백지도 (심플한 배경)

      키워드: "위성지도", "항공지도" → eMapAIR / "일반지도", "기본지도" → eMapBasic

      **2. 지도 확대/축소 (setMapZoom):**
      - 확대: "확대", "더 자세히", "크게" → 현재 레벨 + 2
      - 축소: "축소", "더 넓게", "작게" → 현재 레벨 - 2
      - 최대 확대: "최대한 확대" → 레벨 18
      - 전체 보기: "전체 보기", "전국 보기" → 레벨 8

      **3. 지도 이동 (moveMapByDirection):**
      - 방향 이동: "북쪽으로", "위로", "오른쪽으로" → moveMapByDirection
      - 거리 지정: "동쪽으로 500m", "북쪽으로 1km" → moveMapByDirection

      **키워드 매칭 및 즉시 실행:**

      **✅ 지원되는 기능 - 즉시 도구 호출 (확인 질문 절대 금지):**

      **배경지도 변경 키워드:** "위성지도", "항공지도", "일반지도", "기본지도", "색각지도", "백지도"
      사용자: "위성지도로 바꿔줘" → 즉시 changeBasemap({ basemapId: "eMapAIR" }) 호출
      사용자: "일반지도로 전환" → 즉시 changeBasemap({ basemapId: "eMapBasic" }) 호출
      ❌ 잘못된 응답: "위성지도로 변경할까요?" (확인 질문 금지)
      ✅ 올바른 응답: 즉시 도구 호출 후 "위성지도로 변경했습니다"

      **지도 확대/축소 키워드:** "확대", "축소", "넓게", "자세히", "크게", "작게", "줌인", "줌아웃"
      사용자: "지도 확대해줘" → 즉시 setMapZoom({ zoomLevel: 2, zoomType: "relative", zoomDirection: "in" }) 호출
      사용자: "더 넓게 보여줘" → 즉시 setMapZoom({ zoomLevel: 2, zoomType: "relative", zoomDirection: "out" }) 호출
      사용자: "최대한 확대" → 즉시 setMapZoom({ zoomLevel: 18, zoomType: "absolute", zoomDirection: "in" }) 호출
      ❌ 잘못된 응답: "확대를 진행할까요?" (확인 질문 금지)
      ✅ 올바른 응답: 즉시 도구 호출 후 "지도를 확대했습니다"

      **지도 이동 키워드:** "북쪽으로", "남쪽으로", "동쪽으로", "서쪽으로", "위로", "아래로", "왼쪽으로", "오른쪽으로" + 거리
      사용자: "지도를 북쪽으로 이동" → 즉시 moveMapByDirection({ direction: "north" }) 호출
      사용자: "오른쪽으로 500m" → 즉시 moveMapByDirection({ direction: "right", distance: 500 }) 호출
      ❌ 잘못된 응답: "북쪽으로 이동할까요?" (확인 질문 금지)
      ✅ 올바른 응답: 즉시 도구 호출 후 "북쪽으로 이동했습니다"

      **❌ 지원되지 않는 기능 - 도구 호출 금지, 안내 메시지만:**

      **지도 회전 키워드:** "회전", "돌려줘", "각도", "방향 바꿔", "rotate"
      사용자: "회전해줘" → 지원되지 않음을 안내하고 대안 제시

      **지도 기울기 키워드:** "기울여", "틸트", "3D", "입체", "tilt"
      사용자: "지도 기울여줘" → 지원되지 않음을 안내하고 대안 제시

      **🚨 중요 규칙 - 즉시 실행 원칙:**
      1. **지원되는 기능**: 추가 확인 없이 즉시 해당 도구 호출 (설명이나 "~할까요?" 같은 확인 질문 금지)
      2. **지원되지 않는 기능**: 절대 도구 호출 금지, 제한사항 안내 메시지만 제공
      3. **모호한 요청**: 정말 애매한 경우에만 chooseOption 사용 (명확한 키워드가 있으면 즉시 실행)
      4. **복합 요청**: 지원되는 기능만 추출하여 즉시 처리, 지원되지 않는 부분은 안내

      **실행 예시:**
      - "회전해줘" → 도구 호출 ❌, "지도 회전은 지원되지 않습니다" 안내 ✅
      - "확대해줘" → 즉시 setMapZoom 도구 호출 ✅ (확인 질문 ❌)
      - "지도 확대해줘" → 즉시 setMapZoom 도구 호출 ✅ (확인 질문 ❌)
      - "위성지도로 바꿔줘" → 즉시 changeBasemap 도구 호출 ✅ (확인 질문 ❌)
      - "북쪽으로 이동해줘" → 즉시 moveMapByDirection 도구 호출 ✅ (확인 질문 ❌)
      - "확대하면서 회전해줘" → 즉시 setMapZoom 호출 + 회전 미지원 안내
    `},layer_agent:{tools:{getLayer:m._w,getLayerList:v,getLayerAttributesCount:x,updateLayerStyle:$,removeLayer:C,getLayerAttributes:m.x6,generateCategoricalStyle:D,createLayerFilter:M,...k},system:`
      당신은 레이어 관리 전문가입니다. 레이어 검색/추가/삭제, 스타일 변경, 필터링 등 레이어 관련 작업을 처리합니다.
      ${B.language}
      ${B.tone}
      ${B.interaction}

      **🚨 핵심 원칙: 스타일 변경 요청 구분! 🚨**

      **1. 단순 스타일 변경 (updateLayerStyle 사용):**
      - "레이어를 노란색으로", "빨간색 원으로", "파란색 별모양으로"
      - 전체 레이어에 동일한 스타일 적용
      - 조건이나 필터링 없이 단순 색상/모양 변경
      - 예: "백년가게를 노란색 별모양으로", "건물을 빨간색으로"

      **2. 조건부 스타일링 (generateCategoricalStyle 사용):**
      - "A는 빨간색, B는 파란색", "서울은 노란색, 부산은 파란색"
      - 여러 조건에 따른 다른 스타일 적용
      - 속성값에 따른 분류 스타일링
      - 예: "대학은 파란색, 중학교는 빨간색으로", "강남구는 빨간색, 서초구는 파란색으로"

      **속성 기반 요청 식별 (조건부 스타일링용):**
      - "노후화된", "높은", "오래된", "새로운", "큰", "작은" 등 형용사 포함
      - "~만 보여줘", "~인 것만", "조건에 맞는" 등 필터링 요청
      - **🚨 지역 조건 요청 (최우선 처리): "서울에 있는", "강남구의", "부산시의" 등**

      **⚠️ 스타일 변경 예시:**

      **단순 스타일 변경:**
      - "건물을 빨간색으로" → updateLayerStyle 사용
      - "도로를 파란색으로" → updateLayerStyle 사용
      - "~~레이어를 ~~색으로 ~~모양으로" → updateLayerStyle 사용

      **조건부 스타일링:**
      - "서울에 있는 약국만 빨간색으로" → generateCategoricalStyle 사용
      - "강남구는 빨간색, 서초구는 파란색으로" → generateCategoricalStyle 사용
      - "대학은 파란색, 중학교는 빨간색으로" → generateCategoricalStyle 사용

      **조건부 스타일링 워크플로우 예시: "서울의 노후화된 건물을 보여줘"**
      1. getLayerList(userId="geonuser", layerName="건물")
      2. chooseOption으로 적절한 건물 레이어 선택 ← **반드시 "레이어명|레이어ID" 형태로 제공**
      3. getLayer(선택된레이어ID)로 레이어 추가
      4. getLayerAttributes(선택된레이어ID)로 속성 조회 ← **필수**
      5. getUserInput으로 사용자에게 "노후화" 기준을 물어보기 (getLayerAttributes 결과를 기반으로 예시 제공) ← **필수**
      6. generateCategoricalStyle로 스타일 적용

      **🚨 chooseOption 사용 시 절대 규칙:**
      - 레이어 ID만 제공하는 것 절대 금지 (예: "LR0000004734" ❌)
      - 반드시 사용자가 식별 가능한 형태로 제공
      - 사용자가 어떤 레이어인지 명확히 알 수 있도록 레이어명 포함 필수

      **🚨 절대 금지사항:**
      - getLayerList 호출 시 lyrTySeCode 파라미터 사용 금지
      - 속성 기반 요청에서 getLayerAttributes 생략
      - 추측 기반 속성 컬럼명 사용

      ${function(e){let t=`
**🚨 레이어 도구 사용 핵심 규칙:**

**최우선 원칙:**
- ${P.MAP_STATE_CHECK}
- ${P.WORK_ORDER}
- ${P.DUPLICATE_PREVENTION}

**레이어 ID 관리:**
- 형식: ${P.LAYER_ID_FORMAT}
- 추출: ${P.LAYER_ID_EXTRACTION}

**도구별 핵심 규칙:**
- **getLayerList**: ${j.getLayerList}
- **getLayer**: ${j.getLayer}
- **getLayerAttributes**: ${j.getLayerAttributes}
- **updateLayerStyle**: ${j.updateLayerStyle}
- **removeLayer**: ${j.removeLayer}

**🎯 상세 작업 흐름 (필수 준수):**

**기본 레이어 추가**: ${F.ADD_LAYER}

**속성 정보가 필요한 레이어 작업**: ${F.LAYER_WITH_ATTRIBUTES}

**조건부 스타일링**: ${F.CONDITIONAL_STYLING}

**속성 기반 필터링**: ${F.ATTRIBUTE_BASED_FILTER}

**HIL 도구 활용:**
- ${P.HIL_USAGE}

**에러 방지 체크리스트:**
- Current map state 확인했는가?
- 레이어 ID가 LR로 시작하는 10자리인가?
- 중복 추가를 방지했는가?
- 속성 기반 요청 시 getLayerAttributes를 호출했는가?
- HIL 도구를 적절히 사용했는가?
  `.trim();switch(e){case"unified":return t+`

**🎯 통합 레이어 에이전트 특화 규칙:**

**핵심 임무:**
- 레이어 검색, 추가, 삭제, 스타일 변경, 속성 조회, 필터링 모든 기능 지원
- 현재 지도 상태 우선 확인으로 효율적 작업 흐름 보장
- HIL 도구 적극 활용으로 사용자 경험 향상

**요청 패턴별 처리 방법:**

**1. "기준이 애매한 필터링 요청" 패턴:**
   → getLayerList("건물")
   → chooseOption으로 적절한 레이어 선택
   → getLayer로 레이어 추가
   → getLayerAttributes로 속성 조회
   → "노후화" 관련 속성 컬럼 식별
   → getUserInput(또는 HIL 도구)으로 사용자에게 기준을 물어보기
   → generateCategoricalStyle로 조건부 스타일링 또는 createLayerFilter로 필터링

**2. "높은 건물만 보여줘" 패턴:**
   → 현재 지도에 건물 레이어 확인
   → 없으면 건물 레이어 검색/추가
   → getLayerAttributes로 높이 관련 속성 확인
   → 높이 조건 기반 필터링/스타일링

**3. 지역명 + 속성 조건 패턴 (🚨 지역 조건 우선 처리 🚨):**
   → 지역명을 키워드로 레이어 검색
   → 해당 지역 레이어 우선 선택
   → 속성 조회 후 **지역 관련 속성 컬럼을 우선적으로 식별**
   → **"서울에 있는 약국만 빨간색으로" = 지역 조건(서울) 기준으로 스타일링**
   → **영업상태가 아닌 지역 속성(시도명, 주소 등)을 attributeName으로 사용**

**필수 실행 순서:**
1. 현재 지도 상태 확인 (중복 방지)
2. 키워드 기반 레이어 검색
3. 레이어 추가 (getLayer)
4. 속성 정보 조회 (getLayerAttributes) - 조건부 요청 시 필수
5. 조건부 스타일링/필터링 적용
      `;case"style":return t+`

**🎯 스타일링 특화:**
- 스타일 변경이 주 목적
- 조건부 스타일링 시 속성 정보 필수
- generateCategoricalStyle 사용 시 속성값 검증 필수
      `;default:return t}}("unified")}

    `},density_analysis:{tools:{performDensityAnalysis:m.qc,getLayerList:v,getLayer:m._w,...k},system:`
      ${B.language}
      ${B.tone}
      ${B.interaction}
      ${B.coreRules}
      당신은 밀도 분석 전문가입니다. 특정 레이어의 공간적 밀집도를 분석하여 시각화합니다.

      **🚨 중요: 밀도 분석은 점(Point) 타입 레이어에서만 가능합니다! 🚨**

      **작업 프로세스 (즉시 실행):**
      **중요: 의도분석 결과를 최우선으로 활용하세요!**

      0. **컨텍스트 정보 확인**:
         - Current map state 메시지에서 "의도분석 결과" 섹션 확인

      1. **🚨 즉시 점 타입 레이어 검색 실행 🚨**:
         - **컨텍스트에 레이어 ID가 있는 경우**:
           * getLayer 호출하여 geometryType 확인
           * 점 타입이 아니면 즉시 getLayerList(lyrTySeCode="1") 호출하여 점 타입 레이어만 검색
         - **컨텍스트 정보가 없는 경우**:
           * 첫 응답에서 바로 getLayerList 도구 호출
           * **반드시 lyrTySeCode="1" 파라미터 포함** (점 타입만 검색)
         - **설명 없이 즉시 실행**: getLayerList("스타벅스", lyrTySeCode="1") 같은 형태로 바로 호출
         - 예: "스타벅스 레이어 밀도분석" → 즉시 getLayerList(layerName="스타벅스", lyrTySeCode="1") 호출

      2. **점 타입 레이어 선택**:
         - **검색 결과가 없는 경우**:
           * getUserInput으로 "밀도 분석이 가능한 점 타입 레이어가 없습니다. 다른 키워드로 검색하시겠습니까?" 안내
         - **자동 선택 조건**: 검색 결과가 1개인 경우 자동 선택
         - **chooseOption 사용 조건**: 검색 결과가 여러 개인 경우
           * 반드시 "레이어명 (점 타입)|레이어ID" 형태로 옵션 제공
           * 예: chooseOption("어떤 점 타입 레이어의 밀도를 분석하시겠습니까?", ["스타벅스 서울 매장 (점 타입)|LR000123", "스타벅스 전국 DT 매장 (점 타입)|LR000456"])

      3. **레이어 상세 조회 및 최종 검증**:
         - getLayer로 선택된 레이어의 상세 정보 조회
         - **chooseOption 결과 처리**:
           * 결과가 "레이어명|레이어ID" 형태인 경우 → "|" 뒤의 레이어ID 추출하여 사용
           * 결과가 단순 문자열인 경우 → 해당 문자열을 레이어명으로 처리
         - **최종 포인트 타입 검증**: geometryType이 'point'인지 재확인
         - **포인트 타입이 아닌 경우**:
           * getUserInput으로 "선택하신 레이어는 면/선 타입으로 밀도 분석이 불가능합니다. 점 타입 레이어를 다시 검색하시겠습니까?" 안내
           * 다시 getLayerList(lyrTySeCode="1") 호출

      4. **밀도 분석 수행**:
         - **중요**: getLayer 결과의 'layer' 필드를 performDensityAnalysis의 trgetTypeName으로 사용
         - **예시**: getLayer 결과에서 layer: "Wgeontest4:L100004762" → trgetTypeName: "Wgeontest4:L100004762"
         - **userId**: "geonuser" 고정값 사용
         - **lyrNm**: 선택사항, 기본값은 "밀도 분석 결과"

      **핵심 규칙 (절대 준수):**
      - **🚨 중요: 모든 getLayerList 호출 시 반드시 lyrTySeCode="1" 파라미터 포함! 🚨**
      - **🚨 중요: 사용자 질문에서 키워드를 특정할 수 없다면 getLayerList 호출 시 layerName 파라미터는 비워두세요! 🚨**
      - **getLayer 결과의 'layer' 필드를 performDensityAnalysis의 trgetTypeName으로 반드시 사용**
      - 포인트 타입이 아닌 레이어는 절대 분석하지 말고 사용자에게 점 타입 레이어 선택 유도

      **performDensityAnalysis 파라미터 사용법:**
      - userId: "geonuser" (고정값)
      - trgetTypeName: getLayer 결과의 'layer' 필드 값 (예: "Wgeontest4:L100004762")
      - lyrNm: 결과 레이어 이름 (선택사항, 예: "스타벅스 매장 밀도 분석")

      **예시 시나리오 1: "스타벅스 레이어에 대해서 밀도분석을 수행해줘"**
      1. getLayerList(layerName="스타벅스", lyrTySeCode="1") 호출 (점 타입만 검색)
      2. **검색 결과 처리**:
         - 결과가 없으면: getUserInput("밀도 분석이 가능한 스타벅스 점 타입 레이어가 없습니다. 다른 키워드로 검색하시겠습니까?")
         - 결과가 1개면: 자동 선택
         - 결과가 여러 개면: chooseOption 사용 (반드시 "레이어명 (점 타입)|레이어ID" 형태로)
      3. getLayer(선택된레이어ID) 호출
      4. geometryType이 'point'인지 최종 확인
      5. performDensityAnalysis 호출:
         - userId: "geonuser"
         - trgetTypeName: getLayer 결과의 'layer' 필드 값
         - lyrNm: "스타벅스 매장 밀도 분석"

      **예시 시나리오 2: "밀도분석은 어떻게해"**
      1. getLayerList(lyrTySeCode="1") 호출 (점 타입만 검색, layerName은 비워둠)
      2. chooseOption으로 밀도 분석이 가능한 점 타입 레이어 목록 제시

      **예시 시나리오 3: "밀도분석 요청"**
      1. Current map state에서 Active layers 확인
      2. 선택된 레이어의 geometryType이 'point'인지 확인
      3. 'point'가 아니면 "선택하신 레이어는 면/선 타입으로 밀도 분석이 불가능합니다. 점 타입 레이어를 다시 검색하시겠습니까?" 안내
      4. 'point'이면 performDensityAnalysis 호출

      **사용자 안내 메시지 예시:**
      - "밀도 분석은 점(Point) 타입 레이어에서만 가능합니다."
      - "면 타입 레이어나 선 타입 레이어는 밀도 분석이 지원되지 않습니다."
      - "점 타입 레이어를 선택해주세요."

      **중요**: 절대로 일반 텍스트로 레이어 목록을 나열하지 마세요. 반드시 HIL 도구를 사용하세요!

    `},default_agent:{tools:{...k},system:`
      ${B.language}
      ${B.tone}
      ${B.interaction}
      당신은 친절한 GIS 지도 서비스 어시스턴트입니다. 지도와 직접적으로 관련되지 않은 일반적인 대화나 지원되지 않는 기능 요청을 처리합니다.

      **현재 지원하는 주요 기능:**
      1. **장소 검색 및 길찾기**: 특정 장소 찾기, 경로 안내, 주변 시설 검색
         - 예: "강남역 찾아줘", "여기서 서울역까지 가는 길", "근처 카페 보여줘"
      2. **레이어 관리**: 키워드 기반 레이어 검색 및 추가, 필터링
         - 예: "스타벅스 레이어 추가", "미세먼지 정보 보여줘", "서울의 높은 건물만"
      3. **배경지도 변경**: 일반지도, 위성지도, 색각지도, 백지도 전환
         - 예: "위성지도로 바꿔줘", "배경지도 변경"
      4. **밀도 분석**: 포인트 데이터의 공간적 밀집도 분석 및 시각화
         - 예: "스타벅스 밀도 분석", "인구밀도 분석"
      5. **지도 조작**: 확대/축소, 이동 등 기본 지도 컨트롤

      **현재 지원하지 않는 기능 (명확한 안내 필요):**
      - **데이터 업로드**: 사용자 개인 데이터 파일 업로드 및 추가
      - **데이터 편집**: 기존 레이어 데이터의 수정, 삭제, 편집
      - **데이터 다운로드**: 지도 데이터나 분석 결과의 파일 다운로드
      - **사용자 정의 레이어 생성**: 새로운 레이어 직접 생성
      - **고급 공간 분석**: 버퍼 분석, 오버레이 분석 등 복합 공간 분석

      **주요 임무:**
      1. **일반 대화 처리**: 인사, 감사 표현, 날씨 문의 등 일상적인 대화에 친근하고 자연스럽게 응답
      2. **서비스 안내**: 지원 가능한 기능과 지원하지 않는 기능을 명확히 구분하여 안내
      3. **기능 제한 안내**: 지원하지 않는 기능 요청 시 현재 제한사항을 정중하게 설명하고 대안 제시
      4. **의도 명확화**: 사용자의 요청이 불분명할 때 'getUserInput'이나 'chooseOption'을 사용하여 정확한 의도를 파악
      5. **지도 기능 유도**: 적절한 상황에서 지원 가능한 지도 기능들을 소개하고 안내

      **지원하지 않는 기능 요청 시 응답 가이드라인:**
      - 현재 해당 기능이 지원되지 않음을 정중하게 안내
      - 가능한 경우 유사한 대안 기능 제안
      - 향후 업데이트 계획이 있을 수 있음을 언급 (구체적인 일정은 제시하지 않음)
      - 현재 사용 가능한 관련 기능들을 소개

      **응답 스타일:**
      - 친근하고 도움이 되는 톤으로 대화
      - 한국어로 자연스럽게 소통
      - 제한사항을 설명할 때도 긍정적이고 건설적인 톤 유지
      - 복잡한 요청은 단계별로 안내
      - 지도 관련 질문이면 지원 가능한 해당 기능을 추천

      **예시 응답:**
      사용자: "데이터 업로드 가능해?"
      응답: "죄송하지만 현재 개인 데이터 파일 업로드 기능은 지원하지 않습니다. 대신 기본으로 제공되는 레이어들(건물 정보, 행정경계 등)을 활용하실 수 있어요. 어떤 종류의 데이터를 찾고 계신지 알려주시면 관련된 기존 레이어를 추천해드릴 수 있습니다!"

      사용자: "넌 뭘 잘해?"
      응답: "안녕하세요! 저는 지도 서비스 전문 AI 어시스턴트입니다. 장소 검색, 길찾기, 레이어 추가, 배경지도 변경, 밀도 분석 등 다양한 지도 관련 기능을 도와드릴 수 있어요. 어떤 도움이 필요하신가요?"

      사용자가 편안하게 서비스를 이용할 수 있도록 따뜻하고 전문적인 도움을 제공하되, 기능 제한사항은 명확하고 정직하게 안내하세요.
    `},unsupported_feature:{tools:{...k},system:`
      ${B.language}
      ${B.tone}
      ${B.interaction}
      당신은 지원되지 않는 기능 요청을 전문적으로 처리하는 에이전트입니다.
      사용자가 현재 시스템에서 지원하지 않는 기능을 요청했을 때, 명확하고 친절하게 제한사항을 설명하고 대안을 제시합니다.

      **🚨 절대 지원하지 않는 기능 카테고리:**

      **1. 주변/근처 POI 검색:**
      - "여기 근처에 맛집", "주변 카페", "이 근처 병원", "근처 편의점" 등
      - 현재 위치 기반 주변 시설 검색 기능

      **2. 카테고리별 POI 목록:**
      - "맛집 목록", "카페 목록", "병원 목록", "전체 편의점 목록" 등
      - 특정 카테고리의 전체 시설 목록 제공

      **3. 실시간 정보:**
      - 실시간 교통정보, 교통상황, 정체 정보
      - 실시간 날씨 정보

      **4. 데이터 관리:**
      - 개인 데이터 파일 업로드
      - 기존 레이어 데이터 편집, 수정, 삭제
      - 지도 데이터나 분석 결과 다운로드
      - 사용자 정의 레이어 생성

      **5. 고급 공간분석:**
      - 버퍼 분석 (반경 분석)
      - 오버레이 분석
      - 네트워크 분석
      - 복합 공간 분석

      **6. 고급 지도 기능:**
      - 3D 지도 표시
      - 시간대별 데이터 애니메이션
      - 사용자 정의 심볼 생성
      - 고급 스타일링 옵션

      **응답 가이드라인:**
      1. **명확한 제한사항 설명**: 해당 기능이 현재 지원되지 않음을 정중하게 안내
      2. **현재 시스템 범위 명시**: 지도 서비스의 현재 기능 범위를 명확히 설명
      3. **실제 지원 기능만 제안**: 현재 시스템에서 실제로 사용 가능한 기능만 안내
      4. **잘못된 기대 방지**: 지원하지 않는 기능에 대한 추가 질문이나 기대를 유발하지 않음

      **현재 지원하는 기능 (이것만 안내):**
      - **구체적인 장소 검색**: "강남역 찾아줘", "서울시청 이동"
      - **키워드 기반 레이어 검색**: "스타벅스", "서울", "편의점" 등
      - **두 지점 간 길찾기**: "A에서 B까지 가는 길"
      - **밀도 분석**: 포인트 데이터의 공간적 밀집도 분석
      - **레이어 필터링**: 조건에 따른 데이터 필터링
      - **배경지도 변경**: 일반지도, 위성지도, 색각지도, 백지도

      **응답 예시:**
      "죄송하지만 [요청된 기능]은 현재 지원하지 않습니다.

      현재 저희 지도 서비스에서는 다음 기능들을 이용하실 수 있습니다:
      - 구체적인 장소명 검색 (예: '강남역 찾아줘')
      - 키워드 기반 레이어 추가 (예: '스타벅스 레이어 추가')
      - 두 지점 간 길찾기 (예: '강남역에서 홍대까지')

      이 중에서 도움이 될 만한 기능이 있으시면 말씀해 주세요!"

      사용자의 요청을 이해하고 공감하면서도, 현재 시스템의 한계를 명확히 전달하고
      실제로 도움이 될 수 있는 대안을 적극적으로 제시하세요.
    `}},W=h.z.object({isCompleted:h.z.boolean().describe("사용자의 의도가 충분히 해결되었는지 (도구 호출 여부와 무관)"),reason:h.z.string().describe("완료/미완료 판단 이유"),improvementSuggestions:h.z.array(h.z.string()).optional().describe("미완료 시 개선 제안사항")});async function K(e,t,r){try{let a=["chooseOption","getUserInput","confirmWithCheckbox","getLocation"];if(r.some(e=>a.includes(e.toolName)))return{isCompleted:!0,reason:"사용자와의 상호작용을 진행 중이므로 완료로 판단합니다."};let o=`당신은 AI Agent의 작업 수행 결과를 평가하는 전문가입니다.

      **핵심 평가 원칙:**
      1. 도구 호출 여부와 관계없이 사용자의 의도가 충분히 해결되었는지 판단
      2. "안녕하세요" 같은 간단한 인사는 도구 호출 없이도 완료될 수 있음
      3. 단순하게 "완료" vs "미완료" 두 가지로만 구분

      **완료 판단 기준 (isCompleted: true):**
      - 사용자의 원래 요청이 충분히 해결됨
      - 의도분석에서 제시한 목표가 달성됨
      - 더 이상 추가 작업이 필요하지 않음

      **미완료 판단 기준 (isCompleted: false):**
      - 사용자 요청이 아직 해결되지 않음
      - 필요한 작업이 수행되지 않음
      - 도구 호출 실패나 에러 발생
      - "분석을 시작하겠습니다" 등의 예고만 하고 실제 작업 미수행

      **판단 가이드라인:**
      - 전체 맥락을 종합적으로 고려하여 판단
      - 특정 키워드나 패턴에만 의존하지 말 것
      - 사용자 관점에서 요청이 해결되었는지 중점 평가
      `,n=`다음 Agent 수행 결과를 평가하세요:

**의도분석 메시지:**
${e}

**Agent 응답:**
${t}

**도구 정보:**
${r.length>0?r.map(e=>`toolName: ${e.toolName}, args: ${JSON.stringify(e.args)}`).join("\n"):"도구 호출 없음"}

**도구 호출 결과:**
${r.length>0?r.map(e=>`${JSON.stringify(e.result)}`).join("\n"):""}

**종합 평가 요청:**

**평가 기준**
1. **의도분석 목표 달성도**: 요청된 작업이 실제로 완료되었는가?
2. **작업 진행 상태 파악**:
   - 준비만 하고 핵심 작업 미수행 → isCompleted: false
   - 예: "분석을 시작하겠습니다" 후 performDensityAnalysis 미호출
   - 예: "스타일을 변경하겠습니다" 후 updateLayerStyle 미호출
3. **에러 및 실패 상황**: 도구 호출 실패나 잘못된 사용 → isCompleted: false

**🚨 핵심 판단 원칙:**
- **HIL 도구 호출 시**: 무조건 isCompleted: true (최우선)
- **작업 예고 후 미수행**: isCompleted: false
- **실제 작업 완료**: isCompleted: true
- **에러 발생**: isCompleted: false
`;console.log("=== 평가자 호출 ==="),console.log("시스템 프롬프트:",o),console.log("평가 프롬프트:",n);let{object:i}=await (0,s.pY)({model:(0,b.N)("gpt-4.1-nano"),schema:W,system:o,prompt:n,temperature:0});return i}catch(e){return console.error("평가자 실행 실패:",e),{isCompleted:!1,reason:"평가자 오류로 인한 기본 판단: 작업이 미완료된 것으로 처리",improvementSuggestions:["의도분석 메시지에 따라 필요한 도구를 호출하세요","구체적인 작업을 수행하여 사용자의 요청을 완료하세요"]}}}async function X({model:e,agentName:t,messages:r,stateMessage:a,intentMessage:o,dataStream:n,session:i,enable_smart_navigation:l,isNonGeonProvider:c,iteration:d=0,maxIterations:g=3,onEvaluationComplete:m,chatId:y}){console.log(`=== Agent 실행 (${d+1}/${g}) ===`);let f=q[t],A=[],b="",h=null;return(0,s.gM)({model:e,messages:[{role:"system",content:f.system},a,...r],temperature:0,tools:f.tools,toolCallStreaming:!0,maxSteps:f.maxSteps||5,experimental_transform:(0,s.dF)(),experimental_continueSteps:!0,...c?{}:{providerOptions:{geon:{metadata:{chat_template_kwargs:{enable_thinking:!1}}}}},onStepFinish:({toolCalls:e,text:t,toolResults:r})=>{e&&e.length>0&&A.push(...e),r&&r.length>0&&r.forEach((e,t)=>{let a=A.length-r.length+t;if(A[a]){let t=A[a],r=f.tools[t.toolName];r&&"experimental_toToolResultContent"in r&&r.experimental_toToolResultContent?t.result=r.experimental_toToolResultContent(e.result):t.result=e.result}}),e&&e.length>0&&e.forEach(e=>{n.writeMessageAnnotation({type:"tool_call",toolName:e.toolName,args:e.args,enableSmartNavigation:l})})},onFinish:async({text:e,toolCalls:a,response:l})=>{e&&(b+=e),a&&a.length>0&&A.push(...a),n.writeMessageAnnotation({type:"evaluation_start",iteration:d+1,maxIterations:g,message:"작업 결과를 평가하고 있습니다..."});try{let e=(h=await K(o,b,A)).isCompleted?"작업이 성공적으로 완료되었습니다":"작업이 미완료되어 계속 진행합니다";n.writeMessageAnnotation({type:"evaluation_completed",iteration:d+1,maxIterations:g,isCompleted:h.isCompleted,shouldContinue:!h.isCompleted,message:e,reason:h.reason,improvementSuggestions:h.improvementSuggestions}),m&&m({...h,toolCalls:A,agentResponse:b})}catch(e){console.error("평가자 실행 실패:",e),h={isCompleted:!1,reason:"평가자 실행 중 오류가 발생하여 미완료로 처리",improvementSuggestions:["평가자 오류로 인한 재시도가 필요합니다"]}}if(i.user?.id&&y&&l.messages&&l.messages.length>0)try{let e=(0,s.Kn)({messages:r,responseMessages:l.messages}).slice(r.length).map(e=>{let t=(0,p.lk)();return"assistant"===e.role&&n.writeMessageAnnotation({messageIdFromServer:t}),{id:t,chatId:y,role:e.role,content:e.content,parts:e.parts||[],attachments:e.experimental_attachments||[],createdAt:new Date}});e.length>0&&await (0,u.yM)({messages:e})}catch(e){console.error("메시지 저장 실패:",e)}if(i.user?.id)try{n.writeMessageAnnotation({type:"agent_completed",agent:t,message:h?.isCompleted?"작업이 완료되었습니다.":"작업이 진행 중입니다.",finalEvaluation:h,iteration:d+1,maxIterations:g})}catch(e){console.error("메시지 저장 실패:",e)}}})}async function V({model:e,agentName:t,messages:r,stateMessage:a,intentMessage:o,dataStream:n,session:i,enable_smart_navigation:s,isNonGeonProvider:l,maxIterations:c=3,chatId:u}){let d=[...r],p=0,g=[];for(;p<c;){console.log(`=== 재시도 핸들러: ${p+1}/${c} ===`),console.log("Current Messages:",JSON.stringify(d));let r=null,m=!1;for((await X({model:e,agentName:t,messages:d,stateMessage:a,intentMessage:o,dataStream:n,session:i,enable_smart_navigation:s,isNonGeonProvider:l,iteration:p,maxIterations:c,chatId:u,onEvaluationComplete:e=>{r=e,e.toolCalls&&(g=e.toolCalls),m=!0}})).mergeIntoDataStream(n,{sendReasoning:!0});!m;)await new Promise(e=>setTimeout(e,100));if(r?.isCompleted){console.log("=== 작업 완료 ==="),p>0&&n.writeMessageAnnotation({type:"retry_completed",totalIterations:p+1,maxIterations:c,finalResult:"success",message:"모든 작업이 성공적으로 완료되었습니다"});break}if(p<c-1){if(console.log("=== 재시도 준비 중 ==="),d=[...d,{role:"system",content:function(e){let t=(e.improvementSuggestions||["작업을 완료하기 위해 필요한 도구를 호출하세요"]).join("\n- ");return`🔄 이전 시도가 불완전했습니다. 다음 사항을 개선하여 작업을 완료하세요:

**미완료 이유:**
${e.reason}

**개선사항:**
- ${t}

**중요:** 설명보다는 실제 도구 호출을 통해 작업을 수행하세요.`}(r)}],g.length>0){let e=g.map(e=>{let t=JSON.stringify(e.args),r=e.result?JSON.stringify(e.result).substring(0,200)+"...":"결과 없음";return`${e.toolName}(${t}) → ${r}`}).join("\n");d=[...d,{role:"assistant",content:`이전 시도에서 수행한 작업 결과:
${e}

위 결과를 참고하여 재시도하세요.`}]}n.writeMessageAnnotation({type:"retry_starting",iteration:p+2,maxIterations:c,message:`${p+2}번째 시도를 시작합니다`,reason:r.reason,improvementSuggestions:r.improvementSuggestions||[]}),p++}else{console.log("=== 최대 재시도 횟수 도달 ==="),n.writeMessageAnnotation({type:"retry_limit_reached",totalIterations:c,maxIterations:c,message:`⏰ 최대 재시도 횟수(${c}회)에 도달했습니다`,finalEvaluation:r});break}}return"완료"}let H=100;async function Q(e){let{id:t,messages:r,layers:a,modelId:o,enable_thinking:n,enable_smart_navigation:i}=await e.json(),m=await (0,c.j2)();if(!m||!m.user||!m.user.id)return new Response("Unauthorized",{status:401});let y=(0,l.Lc)("Qwen/Qwen3-4B"),f=(0,d.UT)(o||"Qwen3-4B"),A=!0,b=!1;if(f){let e=(0,d.Bm)(f.id);A=(0,d.t5)(f.id),"openai"===e?(y=(0,l.k5)(f.apiIdentifier),b=!0):y=(0,l.Lc)(f.apiIdentifier)}let h=(0,s.E2)((0,p.Vs)(r)),L=(0,p.Bv)(h);if(!L)return new Response("No user message found",{status:400});if(!await (0,u.TJ)({id:t})){let e=await (0,g.I8)({message:L});await (0,u.yd)({id:t,userId:m.user.id,title:e})}let E={id:(0,p.lk)(),chatId:t,role:L.role,content:"",parts:Array.isArray(L.content)?L.content:[{type:"text",text:L.content}],attachments:[],createdAt:new Date};return await (0,u.yM)({messages:[E]}),(0,s.ke)({execute:async e=>{try{let r=[...h].reverse().find(e=>"assistant"===e.role),o=[{role:"system",content:G.system}];if(r){let e="string"==typeof r.content?r.content:JSON.stringify(r.content);o.push({role:"assistant",content:e})}let l="string"==typeof L.content?L.content:JSON.stringify(L.content);o.push({role:"user",content:l});let{experimental_partialOutputStream:c}=(0,s.gM)({model:y,temperature:0,messages:o,experimental_output:s.k7.object({schema:Y}),onChunk({chunk:t}){"reasoning"===t.type&&e.writeMessageAnnotation(t)},...b?{}:{providerOptions:{geon:{metadata:{chat_template_kwargs:{enable_thinking:n&&A}}}}}}),u={intent:"GENERAL_CONVERSATION",message:"사용자 요청을 처리하고 있습니다..."};for await(let e of c)e.intent&&e.message&&(u={intent:e.intent,message:e.message});e.writeMessageAnnotation({type:"intent_analyzed",intent:u.intent,message:u.message});let d={LAYER_ADD:"layer_agent",LAYER_REMOVE:"layer_agent",LAYER_STYLE:"layer_agent",LAYER_LIST:"layer_agent",LAYER_FILTER:"layer_agent",NAVIGATION:"navigation",MAP_CONTROL:"map_control",BASEMAP_CHANGE:"map_control",DENSITY_ANALYSIS:"density_analysis",GENERAL_CONVERSATION:"default_agent",UNSURE:"default_agent",UNSUPPORTED_FEATURE:"unsupported_feature"}[u.intent];if(console.log(`[ROUTING] Intent: ${u.intent} → Agent: ${d||"null"}`),!d)return void(0,s.gM)({model:y,messages:[{role:"system",content:"당신은 친근하고 도움이 되는 지도 AI 어시스턴트입니다. 사용자와 자연스럽게 대화하세요."},...h],temperature:0,maxSteps:5}).mergeIntoDataStream(e);q[d],e.writeMessageAnnotation({type:"agent_start",agent:d,message:`${d} 에이전트가 작업을 시작합니다`});let p={role:"system",content:`Current map state:
- Active layers: ${a.map(e=>{let t=e?.name||e?.info?.lyrNm||e?.id||"Unknown",r=e?.geometryType||"unknown",a=e?.renderOptions?.style,o="";if(a&&a.rules&&a.rules[0]&&a.rules[0].symbolizers&&a.rules[0].symbolizers[0]){let e=a.rules[0].symbolizers[0],t=[];"Mark"===e.kind&&e.wellKnownName&&t.push({circle:"원형",square:"사각형",triangle:"삼각형",star:"별",cross:"십자",x:"X자"}[e.wellKnownName]||e.wellKnownName),e.color&&t.push(`색상: ${e.color}`),e.radius?t.push(`크기: ${e.radius}px`):e.width&&t.push(`두께: ${e.width}px`),void 0!==e.fillOpacity?t.push(`투명도: ${Math.round(100*e.fillOpacity)}%`):void 0!==e.opacity&&t.push(`투명도: ${Math.round(100*e.opacity)}%`),t.length>0&&(o=`, ${t.join(", ")}`)}else o=", 스타일 정보 없음";return`${t} [ID: ${e?.id}] (${r}${o})`}).join(", ")}
- Total layer length: ${a.length}
- 사용자 정보: {
    userId = '${m.user?.id}'
    insttCode = 'geonpaas'
    userSeCode = '14'
  }

의도분석 결과: ${u.message}
위 계획에 따라 단계별로 작업을 수행하세요.`};await V({model:y,agentName:d,messages:h,stateMessage:p,intentMessage:u.message,dataStream:e,session:m,enable_smart_navigation:i,isNonGeonProvider:b,chatId:t})}catch(e){console.error("Agent processing error:",e)}},onError:e=>(console.error("Agent processing error:",e),s.$o.isInstance(e))?"The model tried to call a unknown tool.":s.eK.isInstance(e)?"The model called a tool with invalid arguments.":s.TO.isInstance(e)?"An error occurred during tool execution.":"An unknown error occurred."})}async function J(e){let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return new Response("Not Found",{status:404});let a=await (0,c.j2)();if(!a||!a.user)return new Response("Unauthorized",{status:401});try{if((await (0,u.TJ)({id:r})).userId!==a.user.id)return new Response("Unauthorized",{status:401});return await (0,u.qQ)({id:r}),new Response("Chat deleted",{status:200})}catch(e){return new Response("An error occurred while processing your request",{status:500})}}let Z=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/(map)/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/(map)/api/chat/route"},resolvedPagePath:"C:\\chatbot\\front-chat\\app\\(map)\\api\\chat\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:ee,workUnitAsyncStorage:et,serverHooks:er}=Z;function ea(){return(0,i.patchFetch)({workAsyncStorage:ee,workUnitAsyncStorage:et})}},16387:(e,t,r)=>{r.d(t,{Jh:()=>a,j5:()=>o,nK:()=>i,rZ:()=>n});let a=()=>{let e=process.env.GEON_API_BASE_URL||"http://121.163.19.101:14090",t=process.env.GEON_API_KEY;return t||console.warn("GEON_API_KEY가 설정되지 않았습니다."),{baseUrl:e,headers:{crtfckey:t||""},auth:{userId:process.env.GEON_API_USER_ID||"geonuser",password:process.env.GEON_API_USER_PASSWORD||"wavus1234!"}}},o=(e,t)=>{let r=t||a();return r.headers.crtfckey&&e.append("crtfckey",r.headers.crtfckey),e},n=e=>({"Content-Type":"application/json",...(e||a()).headers}),i=e=>(e||a()).auth.userId},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33769:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{decryptActionBoundArgs:function(){return y},encryptActionBoundArgs:function(){return m}}),r(35317);let a=r(35306),o=r(46544),n=r(56950),i=r(1997),s=r(63033),l=r(23306),c=function(e){return e&&e.__esModule?e:{default:e}}(r(79907)),u=new TextEncoder,d=new TextDecoder;async function p(e,t){let r=await (0,i.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let a=atob(t),o=a.slice(0,16),n=a.slice(16),s=d.decode(await (0,i.decrypt)(r,(0,i.stringToUint8Array)(o),(0,i.stringToUint8Array)(n)));if(!s.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return s.slice(e.length)}async function g(e,t){let r=await (0,i.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let a=new Uint8Array(16);s.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(a));let o=(0,i.arrayBufferToString)(a.buffer),n=await (0,i.encrypt)(r,a,u.encode(e+t));return btoa(o+(0,i.arrayBufferToString)(n))}let m=c.default.cache(async function e(t,...r){let{clientModules:o}=(0,i.getClientReferenceManifestForRsc)(),c=Error();Error.captureStackTrace(c,e);let u=!1,d=s.workUnitAsyncStorage.getStore(),p=(null==d?void 0:d.type)==="prerender"?(0,l.createHangingInputAbortSignal)(d):void 0,m=await (0,n.streamToString)((0,a.renderToReadableStream)(r,o,{signal:p,onError(e){(null==p||!p.aborted)&&(u||(u=!0,c.message=e instanceof Error?e.message:String(e)))}}),p);if(u)throw c;if(!d)return g(t,m);let y=(0,s.getPrerenderResumeDataCache)(d),f=(0,s.getRenderResumeDataCache)(d),A=t+m,b=(null==y?void 0:y.encryptedBoundArgs.get(A))??(null==f?void 0:f.encryptedBoundArgs.get(A));if(b)return b;let h="prerender"===d.type?d.cacheSignal:void 0;null==h||h.beginRead();let L=await g(t,m);return null==h||h.endRead(),null==y||y.encryptedBoundArgs.set(A,L),L});async function y(e,t){let r,a=await t,n=s.workUnitAsyncStorage.getStore();if(n){let t="prerender"===n.type?n.cacheSignal:void 0,o=(0,s.getPrerenderResumeDataCache)(n),i=(0,s.getRenderResumeDataCache)(n);(r=(null==o?void 0:o.decryptedBoundArgs.get(a))??(null==i?void 0:i.decryptedBoundArgs.get(a)))||(null==t||t.beginRead(),r=await p(e,a),null==t||t.endRead(),null==o||o.decryptedBoundArgs.set(a,r))}else r=await p(e,a);let{edgeRscModuleMapping:l,rscModuleMapping:c}=(0,i.getClientReferenceManifestForRsc)();return await (0,o.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(u.encode(r)),(null==n?void 0:n.type)==="prerender"?n.renderSignal.aborted?e.close():n.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:c,serverModuleMap:(0,i.getServerModuleMap)()}})}},42449:e=>{e.exports=require("pg")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},44919:(e,t,r)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a.registerServerReference}});let a=r(35306)},47841:(e,t,r)=>{r.d(t,{j2:()=>d,Y9:()=>u});var a=r(393),o=r(95983);a.xz;let n="tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh",i="https://gsapi.geon.kr/smt";async function s(e,t){let r=new URLSearchParams({crtfckey:n,userId:e,password:t}),a=await fetch(`${i}/login/validation?${r.toString()}`,{method:"POST",headers:{"Content-Type":"application/json",crtfckey:n}}),o=await a.json();if(!a.ok)throw Error("로그인 검증에 실패했습니다.");return o}async function l(e){let t=new URLSearchParams({crtfckey:n,userId:e}),r=await fetch(`${i}/users/id?${t.toString()}`,{method:"GET",headers:{"Content-Type":"application/json",crtfckey:n}}),a=await r.json();if(!r.ok)throw Error("사용자 정보를 가져오는데 실패했습니다.");return a}let c=[(0,o.A)({credentials:{},async authorize({id:e,password:t}){try{let r=process.env.FRONTEND_LOGIN_USER_ID||"admin",o=process.env.FRONTEND_LOGIN_PASSWORD||"password1234";if(e===r&&t===o)return{id:r,name:"GeOn City",email:"@example.com",userId:r,userNm:"GeOn City",emailaddr:"@example.com",userSeCode:"14",userSeCodeNm:"관리자",userImage:null,insttCode:"GEON",insttNm:"GeOn",insttUrl:null,message:"로그인 성공"};if("geonuser"===e){let r=await s(e,t);if(!r.result.isValid)throw new a.xz(r.result.message);let o=await l(e);if(200!==o.code)return new a.xz(o.result.message);return{...o.result,id:o.result.userId,name:o.result.userNm||e,email:o.result.emailaddr||`${o.result.userNm}`}}throw new a.xz("admin 또는 geonuser 계정으로만 로그인할 수 있습니다.")}catch(e){throw console.error("Auth error:",e),e}}})];c.map(e=>{if("function"!=typeof e)return{id:e.id,name:e.name};{let t=e();return{id:t.id,name:t.name}}}).filter(e=>"credentials"!==e.id);let{handlers:u,auth:d,signIn:p,signOut:g}=(0,a.Ay)({pages:{signIn:"/login",newUser:"/"},providers:[],callbacks:{authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,a=t.pathname.startsWith("/geon-2d-map"),o=t.pathname.startsWith("/login");return"/"===t.pathname||r&&o?Response.redirect(new URL("/geon-2d-map",t)):!!o||!a||r}},providers:c,session:{strategy:"jwt",maxAge:1800},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.id=t.id),e),session:async({session:e,token:t})=>(e.user&&(e.user.id=t.id),e)}})},55511:e=>{e.exports=require("crypto")},61401:(e,t,r)=>{r.d(t,{I8:()=>u,Tj:()=>c,Yc:()=>p,er:()=>d,qi:()=>l});var a=r(44919);r(33769);var o=r(94062),n=r(28538),i=r(88668),s=r(45346);async function l(e){(await (0,s.UL)()).set("model-id",e)}async function c(e){(await (0,s.UL)()).set("dev-model-id",e)}async function u({message:e}){let{text:t}=await (0,i.Df)({model:(0,o.N)("gpt-4o-mini"),system:`

    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,prompt:JSON.stringify(e)});return t}async function d({id:e}){let[t]=await (0,n.kA)({id:e});await (0,n.wA)({chatId:t.chatId,timestamp:t.createdAt})}async function p({chatId:e,visibility:t}){await (0,n.$)({chatId:e,visibility:t})}(0,r(81861).D)([l,c,u,d,p]),(0,a.A)(l,"401f30ce6fdede15f2c53da7aa771517b099ae106a",null),(0,a.A)(c,"40b8bc00089ac88f5caa786935681a3e95192f64cd",null),(0,a.A)(u,"403e51ddf25daebec3f88e80c97dc81961e27479e5",null),(0,a.A)(d,"4079d6eb871135bc43af841132ea65c07ef40d496e",null),(0,a.A)(p,"407dc812db3fb2af268db495da0484e2aab60500da",null)},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67234:(e,t)=>{function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},75077:(e,t,r)=>{r.d(t,{Bm:()=>s,Jn:()=>a,UT:()=>n,oQ:()=>o,t5:()=>i});let a=[{id:"Qwen3-14B",label:"Qwen3 14B",apiIdentifier:"Qwen/Qwen2.5-14B",description:"Qwen3 14B 모델입니다.",provider:"geon",capabilities:{reasoning:!1,streaming:!0,tools:!0,vision:!1}},{id:"Qwen3-4B",label:"Qwen3 (추론)",apiIdentifier:"Qwen/Qwen3-4B",description:"Qwen3-4B 추론 모델입니다.",provider:"geon",capabilities:{reasoning:!0,streaming:!0,tools:!0,vision:!1}},{id:"gpt-4.1-nano",label:"GPT 4.1 Nano",apiIdentifier:"gpt-4.1-nano",description:"OpenAI의 GPT 4.1 Nano 모델입니다.",provider:"openai",capabilities:{reasoning:!1,streaming:!0,tools:!0,vision:!1}}],o="Qwen3-4B";function n(e){return a.find(t=>t.id===e)}function i(e){let t=function(e){let t=n(e);return t?.capabilities}(e);return t?.reasoning??!1}function s(e){let t=n(e);return t?.provider}},77598:e=>{e.exports=require("node:crypto")},81861:(e,t)=>{function r(e){for(let t=0;t<e.length;t++){let r=e[t];if("function"!=typeof r)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof r}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return r}})},92959:(e,t,r)=>{r.r(t),r.d(t,{"401f30ce6fdede15f2c53da7aa771517b099ae106a":()=>a.qi,"403e51ddf25daebec3f88e80c97dc81961e27479e5":()=>a.I8,"4079d6eb871135bc43af841132ea65c07ef40d496e":()=>a.er,"407dc812db3fb2af268db495da0484e2aab60500da":()=>a.Yc,"40b8bc00089ac88f5caa786935681a3e95192f64cd":()=>a.Tj});var a=r(61401)},96451:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return n},normalizeRscURL:function(){return i}});let a=r(67234),o=r(54224);function n(e){return(0,a.ensureLeadingSlash)(e.split("/").reduce((e,t,r,a)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===a.length-1?e:e+"/"+t,""))}function i(e){return e.replace(/\.rsc($|\?)/,"$1")}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[332,481,741,115,746,861,180,46,929,343,669,501],()=>r(15058));module.exports=a})();