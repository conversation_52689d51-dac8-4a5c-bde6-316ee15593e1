{"/(auth)/api/auth/[...nextauth]/route": "app/(auth)/api/auth/[...nextauth]/route.js", "/(map)/api/health/route": "app/(map)/api/health/route.js", "/(map)/api/history/route": "app/(map)/api/history/route.js", "/(map)/api/layer/detailed-info/route": "app/(map)/api/layer/detailed-info/route.js", "/(map)/api/vote/route": "app/(map)/api/vote/route.js", "/(map)/geon-2d-map/[id]/page": "app/(map)/geon-2d-map/[id]/page.js", "/manifest.webmanifest/route": "app/manifest.webmanifest/route.js"}