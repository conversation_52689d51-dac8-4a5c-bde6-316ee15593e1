"use client";

import React, { useState, useEffect } from "react";
import { Database, Info, Hash, Type, BarChart3, Loader2, Search, Grid3X3, List, Table, Calendar, MapPin, ArrowUpDown, ChevronLeft, ChevronRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface LayerColumn {
  name: string;
  description: string;
  type: string;
  editable: boolean;
  required: boolean;
  minValue: number | null;
  maxValue: number | null;
  groupCode: string | null;
  groupName: string | null;
}

interface DetailedLayerInfo {
  columns: Array<{
    name: string;
    type: string;
    description?: string;
    required?: boolean;
    editable?: boolean;
    minValue?: number;
    maxValue?: number;
  }>;
  data: Record<string, any>[];
  totalCount: number;
  pageInfo: {
    pageSize: number;
    pageIndex: number;
  };
  pkColumnName?: string;
}

interface LayerAttributesDetailDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  columns: LayerColumn[];
  data: Record<string, any>[];
  layerInfo?: {
    lyrId: string;
    cntntsId: string;
    namespace: string;
    userId?: string;
  } | null;
}

// 속성 타입에 따른 아이콘 반환
const getAttributeIcon = (type: string) => {
  const lowerType = type.toLowerCase();
  if (lowerType.includes('string') || lowerType.includes('text')) {
    return <Type className="h-3 w-3" />;
  } else if (lowerType.includes('number') || lowerType.includes('int') || lowerType.includes('double')) {
    return <Hash className="h-3 w-3" />;
  } else if (lowerType.includes('date') || lowerType.includes('time')) {
    return <Calendar className="h-3 w-3" />;
  } else if (lowerType.includes('geometry') || lowerType.includes('point') || lowerType.includes('polygon')) {
    return <MapPin className="h-3 w-3" />;
  }
  return <Database className="h-3 w-3" />;
};

// 속성 타입에 따른 색상 반환
const getAttributeTypeColor = (type: string) => {
  const lowerType = type.toLowerCase();
  if (lowerType.includes('string') || lowerType.includes('text')) {
    return 'bg-blue-100/80 text-blue-700 border-blue-200/60';
  } else if (lowerType.includes('number') || lowerType.includes('int') || lowerType.includes('double')) {
    return 'bg-green-100/80 text-green-700 border-green-200/60';
  } else if (lowerType.includes('date') || lowerType.includes('time')) {
    return 'bg-purple-100/80 text-purple-700 border-purple-200/60';
  } else if (lowerType.includes('geometry') || lowerType.includes('point') || lowerType.includes('polygon')) {
    return 'bg-orange-100/80 text-orange-700 border-orange-200/60';
  }
  return 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60';
};

export function LayerAttributesDetailDialog({
  isOpen,
  onOpenChange,
  columns,
  data,
  layerInfo,
}: LayerAttributesDetailDialogProps) {
  const [detailedInfo, setDetailedInfo] = useState<DetailedLayerInfo | null>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [detailsError, setDetailsError] = useState<string | null>(null);

  // UX 개선을 위한 추가 상태
  const [selectedAttribute, setSelectedAttribute] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'data' | 'attributes'>('data');
  const [dataFilter, setDataFilter] = useState<string>('');

  // 페이지네이션 상태
  const [currentPageIndex, setCurrentPageIndex] = useState(1);
  const [currentPageSize, setCurrentPageSize] = useState(50);

  // 데이터 테이블 컬럼 정의 생성
  const createDataTableColumns = (): ColumnDef<Record<string, any>>[] => {
    const currentColumns = detailedInfo?.columns || columns;
    return currentColumns.map((column) => ({
      accessorKey: column.name,
      header: ({ column: col }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => col.toggleSorting(col.getIsSorted() === "asc")}
            className="h-auto p-1 font-medium text-left justify-start w-full"
          >
            <div className="flex flex-col items-start gap-1 w-full min-w-0">
              {/* 첫 번째 줄: 컬럼명 + 정렬 아이콘 + PK 뱃지 */}
              <div className="flex items-center gap-1 w-full min-w-0">
                {getAttributeIcon(column.type)}
                <span className="text-sm font-medium truncate flex-1 min-w-0">{column.name}</span>
                {detailedInfo && column.name === detailedInfo.pkColumnName && (
                  <Badge variant="outline" className="text-[10px] px-1 py-0 bg-yellow-50 text-yellow-700 border-yellow-200 flex-shrink-0">
                    PK
                  </Badge>
                )}
                <ArrowUpDown className="h-3 w-3 flex-shrink-0 text-muted-foreground" />
              </div>
              
              {/* 두 번째 줄: 타입 뱃지 + 설명 */}
              <div className="flex items-center gap-1 w-full min-w-0">
                <Badge variant="outline" className={cn("text-[10px] px-1 py-0 flex-shrink-0", getAttributeTypeColor(column.type))}>
                  {column.type}
                </Badge>
                {column.description && column.description !== column.name && (
                  <span className="text-[11px] text-muted-foreground truncate flex-1 min-w-0">
                    {column.description}
                  </span>
                )}
              </div>
            </div>
          </Button>
        )
      },
      cell: ({ getValue }) => {
        const value = getValue();
        const displayValue = value !== null && value !== undefined ? String(value) : '-';
        return (
          <div className="max-w-[200px] min-w-0">
            <span className="text-xs block truncate" title={displayValue}>
              {displayValue}
            </span>
          </div>
        );
      },
      minSize: 120,
      maxSize: 300,
      
    }));
  };

  // API에서 상세 정보를 가져오는 함수
  const fetchDetailedInfo = async (pageIndex: number = currentPageIndex, pageSize: number = currentPageSize) => {
    // 레이어 정보가 있는 경우에만 API 호출
    if (layerInfo) {
      setIsLoadingDetails(true);
      setDetailsError(null);

      try {
        const { lyrId, cntntsId, namespace, userId = 'geonuser' } = layerInfo;

        const response = await fetch('/api/layer/detailed-info', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            lyrId,
            namespace,
            cntntsId,
            pageIndex,
            pageSize,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        setDetailedInfo(data);
      } catch (error: any) {
        console.error('Error fetching detailed info:', error);
        setDetailsError(error.message || '상세 정보를 가져오는 중 오류가 발생했습니다.');
      } finally {
        setIsLoadingDetails(false);
      }
    } else {
      // 레이어 정보가 없는 경우 안내 메시지만 설정
      setDetailsError('현재는 기본 속성 정보만 표시됩니다. 더 자세한 정보를 보려면 레이어 관리 기능을 이용해주세요.');
    }
  };

  // 페이지네이션 핸들러 함수들
  const handlePageChange = (newPageIndex: number) => {
    setCurrentPageIndex(newPageIndex);
    fetchDetailedInfo(newPageIndex, currentPageSize);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setCurrentPageSize(newPageSize);
    setCurrentPageIndex(1); // 페이지 크기 변경 시 첫 페이지로 이동
    fetchDetailedInfo(1, newPageSize);
  };

  // 페이지네이션 정보 계산
  const totalPages = detailedInfo ? Math.ceil(detailedInfo.totalCount / currentPageSize) : 0;
  const hasNextPage = currentPageIndex < totalPages;
  const hasPrevPage = currentPageIndex > 1;

  // Dialog가 열릴 때 상세 정보 로드
  useEffect(() => {
    if (isOpen && !detailedInfo && !isLoadingDetails) {
      fetchDetailedInfo();
    }
  }, [isOpen]);

  // Dialog가 닫힐 때 상태 초기화
  useEffect(() => {
    if (!isOpen) {
      setDetailedInfo(null);
      setCurrentPageIndex(1);
      setCurrentPageSize(50);
      setDetailsError(null);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[95vw] max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            레이어 속성 상세 정보
          </DialogTitle>
          <DialogDescription>
            {detailedInfo ? (
              <>총 {detailedInfo.columns.length}개의 속성과 {detailedInfo.totalCount}개의 데이터 행이 있습니다. (페이지 {currentPageIndex}/{totalPages})</>
            ) : (
              <>기본 정보: {columns.length}개의 속성과 {data.length}개의 데이터 행</>
            )}
          </DialogDescription>
        </DialogHeader>

        {/* 탭 컨테이너 - 오버플로우 처리 개선 */}
        <div className="flex-1 flex flex-col min-h-0 mt-4 overflow-hidden">
          <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'data' | 'attributes')} className="flex flex-col h-full overflow-hidden">
            <TabsList className="grid w-full grid-cols-2 flex-shrink-0">
              <TabsTrigger value="data" className="flex items-center gap-2">
                <Grid3X3 className="h-4 w-4" />
                데이터
              </TabsTrigger>
              <TabsTrigger value="attributes" className="flex items-center gap-2">
                <List className="h-4 w-4" />
                속성 목록
              </TabsTrigger>
            </TabsList>
            
            {/* 데이터 탭 - 고정 높이 스크롤 구조 */}
            <TabsContent value="data" className="flex-1 flex flex-col min-h-0 space-y-4 mt-4 overflow-hidden">
              {/* 로딩 상태 */}
              {isLoadingDetails && (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-neutral-600">상세 정보를 불러오는 중...</span>
                  </div>
                </div>
              )}

              {/* 에러 상태 */}
              {detailsError && (
                <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded flex-shrink-0">
                  <Info className="h-4 w-4 text-amber-600" />
                  <div>
                    <p className="text-sm font-medium text-amber-900">알림</p>
                    <p className="text-xs text-amber-700">{detailsError}</p>
                  </div>
                </div>
              )}

              {/* 데이터 테이블 - 고정 높이 설정 */}
              {!isLoadingDetails && (
                <div className="flex-1 min-h-0 overflow-hidden flex flex-col">
                  <div className="flex-1 min-h-0 overflow-hidden">
                    <DataTable
                      columns={createDataTableColumns()}
                      data={detailedInfo?.data || data}
                      searchKey={columns.length > 0 ? columns[0].name : undefined}
                      searchPlaceholder="데이터 검색..."
                    />
                  </div>

                  {/* 페이지네이션 컨트롤 */}
                  {detailedInfo && detailedInfo.totalCount > 0 && (
                    <div className="flex items-center justify-between px-2 py-3 border-t bg-neutral-50/50 flex-shrink-0">
                      <div className="flex items-center gap-2 text-sm text-neutral-600">
                        <span>페이지 크기:</span>
                        <Select
                          value={currentPageSize.toString()}
                          onValueChange={(value) => handlePageSizeChange(Number(value))}
                        >
                          <SelectTrigger className="w-20 h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="25">25</SelectItem>
                            <SelectItem value="50">50</SelectItem>
                            <SelectItem value="100">100</SelectItem>
                            <SelectItem value="200">200</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-sm text-neutral-600">
                          총 {detailedInfo.totalCount}개 중 {((currentPageIndex - 1) * currentPageSize) + 1}-{Math.min(currentPageIndex * currentPageSize, detailedInfo.totalCount)}개 표시
                        </span>

                        <div className="flex items-center gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(currentPageIndex - 1)}
                            disabled={!hasPrevPage}
                            className="h-8 w-8 p-0"
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>

                          <span className="text-sm font-medium px-2">
                            {currentPageIndex} / {totalPages}
                          </span>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(currentPageIndex + 1)}
                            disabled={!hasNextPage}
                            className="h-8 w-8 p-0"
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {!detailedInfo && !isLoadingDetails && data.length > 10 && (
                <div className="text-center text-neutral-500 text-xs py-2 flex-shrink-0">
                  ... 및 더 많은 데이터가 있습니다. 상세 정보를 불러오면 더 많은 데이터를 확인할 수 있습니다.
                </div>
              )}
            </TabsContent>

            {/* 속성 목록 탭 - 오버플로우 처리 개선 */}
            <TabsContent value="attributes" className="flex-1 flex flex-col min-h-0 space-y-4 mt-4 overflow-hidden">
              <div className="flex items-center gap-2 flex-shrink-0">
                <Search className="h-4 w-4 text-neutral-500" />
                <Input
                  placeholder="속성 이름으로 검색..."
                  value={dataFilter}
                  onChange={(e) => setDataFilter(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              {/* 카드 목록 - 스크롤 영역 제한 */}
              <div className="flex-1 min-h-0 overflow-y-auto pr-2">
                <div className="grid gap-3">
                  {(detailedInfo?.columns || columns)
                    .filter(column =>
                      dataFilter === '' ||
                      column.name.toLowerCase().includes(dataFilter.toLowerCase()) ||
                      (column.description && column.description.toLowerCase().includes(dataFilter.toLowerCase()))
                    )
                    .map((column, index) => (
                      <Card
                        key={index}
                        className={cn(
                          "cursor-pointer transition-all hover:shadow-md flex-shrink-0",
                          selectedAttribute === column.name && "ring-2 ring-blue-500"
                        )}
                        onClick={() => setSelectedAttribute(selectedAttribute === column.name ? null : column.name)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex items-start gap-3 min-w-0 flex-1">
                              <div className={cn("w-3 h-3 rounded-full flex-shrink-0 mt-0.5", getAttributeTypeColor(column.type).split(' ')[0])}>
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  {getAttributeIcon(column.type)}
                                  <span className="text-sm font-semibold text-neutral-900 truncate flex-1">{column.name}</span>
                                  <div className="flex items-center gap-1 flex-shrink-0">
                                    {detailedInfo && column.name === detailedInfo.pkColumnName && (
                                      <Badge variant="outline" className="text-xs bg-yellow-100/80 text-yellow-700 border-yellow-200/60">
                                        PK
                                      </Badge>
                                    )}
                                    {column.required && (
                                      <Badge variant="outline" className="text-xs bg-red-100/80 text-red-700 border-red-200/60">
                                        필수
                                      </Badge>
                                    )}
                                    {column.editable && (
                                      <Badge variant="outline" className="text-xs bg-green-100/80 text-green-700 border-green-200/60">
                                        편집가능
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                
                                {column.description && column.description !== column.name && (
                                  <p className="text-xs text-neutral-600 mb-2 break-words">{column.description}</p>
                                )}

                                {(column.minValue !== undefined || column.maxValue !== undefined) && (
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="text-xs bg-blue-100/80 text-blue-700 border-blue-200/60">
                                      범위: {column.minValue ?? '∞'} ~ {column.maxValue ?? '∞'}
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            </div>
                            <Badge variant="outline" className={cn("text-xs flex-shrink-0", getAttributeTypeColor(column.type))}>
                              {column.type}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
