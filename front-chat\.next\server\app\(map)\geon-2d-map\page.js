"use strict";(()=>{var e={};e.id=866,e.ids=[866],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},42449:e=>{e.exports=require("pg")},55511:e=>{e.exports=require("crypto")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72779:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p,metadata:()=>d});var n=r(33626),a=r(34934),o=r(45346),i=r(75077),s=r(85457);let d={title:"말로 만드는 지도",description:"AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.",keywords:["지도","AI 지도","대화형 지도","지리정보","GIS","자연어 검색","지도 분석"],openGraph:{title:"말로 만드는 지도 | 업무지원(챗봇)",description:"AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다.",type:"website"},twitter:{card:"summary_large_image",title:"말로 만드는 지도 | 업무지원(챗봇)",description:"AI와 대화하며 지도를 탐색하고 분석하세요. 자연어로 지리 정보를 검색하고 시각화할 수 있는 혁신적인 지도 서비스입니다."}};async function p(){let e=(0,s.lk)(),t=await (0,o.UL)(),r=t.get("model-id")?.value,d=i.Jn.find(e=>e.id===r)?.id||i.oQ;return(0,n.jsx)(a.ChatMap,{id:e,initialMessages:[],selectedModelId:d,selectedVisibilityType:"private",isReadOnly:!1},e)}},73136:e=>{e.exports=require("node:url")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},86728:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var n=r(49994),a=r(18765),o=r(42117),i=r.n(o),s=r(91962),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);r.d(t,d);let p={children:["",{children:["(map)",{children:["geon-2d-map",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72779)),"C:\\chatbot\\front-chat\\app\\(map)\\geon-2d-map\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72516)),"C:\\chatbot\\front-chat\\app\\(map)\\geon-2d-map\\layout.tsx"]}]},{forbidden:[()=>Promise.resolve().then(r.t.bind(r,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,96592)),"C:\\chatbot\\front-chat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76532)),"C:\\chatbot\\front-chat\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,81494,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,61135,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,l=["C:\\chatbot\\front-chat\\app\\(map)\\geon-2d-map\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(map)/geon-2d-map/page",pathname:"/geon-2d-map",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[332,481,741,115,180,228,309,251,753,391,152,352],()=>r(86728));module.exports=n})();