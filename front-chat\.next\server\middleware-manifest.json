{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/|_next\\/|_static\\/|_vercel|[\\w-]+\\.\\w+).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GcbsATTd8lvck/zFnWhNeUaBXdbsC87dq0HdfZ6yHFk=", "__NEXT_PREVIEW_MODE_ID": "a8fba5e56f7b995dd0bbda214141094b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "83f5a63a11c4c81abaca4e57da12665e02a38346bd9e3f22bc9f9498ce868996", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8fe654cbe316053d2d5508478dc3dff73df26c2e8fd0939a70fbc6f9fae20a7f"}}}, "sortedMiddleware": ["/"], "functions": {}}