{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*){(\\\\.json)}?", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GcbsATTd8lvck/zFnWhNeUaBXdbsC87dq0HdfZ6yHFk=", "__NEXT_PREVIEW_MODE_ID": "e24b4049c9af923c45ef84bfb7fe1dab", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "411d7f604cdf6a010b7a0589dfb93d9bfa940898946dc3820a1809d55c895513", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "215ec75998b28686830bb949f9e2da5c77020085f1415d234b543a774c0081cf"}}}, "instrumentation": null, "functions": {}}